/**
 * Data Service Interface
 *
 * Bu interface, uygulamanın tüm veri işlemlerini yöneten
 * ana soyutlamadır. SOLID prensiplerine uygun olarak
 * implementasyon detaylarından bağımsız bir API sunar.
 *
 * Özellikler:
 * - CRUD operasyonları (Create, Read, Update, Delete)
 * - Query ve filtreleme desteği
 * - Batch işlemler
 * - Real-time data subscription
 * - Transaction desteği
 * - Tip güven<PERSON>
 */

/**
 * Veri kaynağı türleri
 */
export type DataSourceType = 'firebase' | 'restapi' | 'mock';

/**
 * Query operatörleri
 */
export type QueryOperator =
  | '=='
  | '!='
  | '<'
  | '<='
  | '>'
  | '>='
  | 'in'
  | 'not-in'
  | 'array-contains'
  | 'array-contains-any';

/**
 * Sıralama yönü
 */
export type OrderDirection = 'asc' | 'desc';

/**
 * Query koşulu
 */
export interface QueryCondition {
  /** Alan adı */
  field: string;
  /** Operatör */
  operator: QueryOperator;
  /** Değer */
  value: unknown;
}

/**
 * Sıralama koşulu
 */
export interface OrderCondition {
  /** Alan adı */
  field: string;
  /** Sıralama yönü */
  direction: OrderDirection;
}

/**
 * Query seçenekleri
 */
export interface QueryOptions {
  /** Where koşulları */
  where?: QueryCondition[];
  /** Sıralama koşulları */
  orderBy?: OrderCondition[];
  /** Limit */
  limit?: number;
  /** Offset (sayfalama için) */
  offset?: number;
  /** Başlangıç cursor (Firebase için) */
  startAfter?: unknown;
  /** Bitiş cursor (Firebase için) */
  endBefore?: unknown;
}

/**
 * Batch işlem türleri
 */
export type BatchOperationType = 'create' | 'update' | 'delete';

/**
 * Batch işlem
 */
export interface BatchOperation<T = unknown> {
  /** İşlem türü */
  type: BatchOperationType;
  /** Collection adı */
  collection: string;
  /** Document ID (update/delete için) */
  id?: string;
  /** Data (create/update için) */
  data?: Partial<T>;
}

/**
 * Subscription callback
 */
export type SubscriptionCallback<T> = (data: T[]) => void;

/**
 * Subscription unsubscribe fonksiyonu
 */
export type UnsubscribeFunction = () => void;

/**
 * Transaction callback
 */
export type TransactionCallback<T> = (transaction: ITransaction) => Promise<T>;

/**
 * Transaction interface
 */
export interface ITransaction {
  /**
   * Document okur
   */
  get<T>(collection: string, id: string): Promise<T | null>;

  /**
   * Document oluşturur
   */
  create<T>(collection: string, data: T): string;

  /**
   * Document günceller
   */
  update<T>(collection: string, id: string, data: Partial<T>): void;

  /**
   * Document siler
   */
  delete(collection: string, id: string): void;
}

/**
 * Data Service Interface
 *
 * Tüm veri işlemlerini yöneten ana interface.
 * Firebase, REST API veya Mock implementasyonları
 * bu interface'i implement eder.
 */
export interface IDataService {
  // ===========================================
  // TEMEL CRUD OPERASYONLARI
  // ===========================================

  /**
   * Yeni document oluşturur
   * @param collection Collection adı
   * @param data Document verisi
   * @returns Document ID
   */
  create<T>(collection: string, data: T): Promise<string>;

  /**
   * Document'i ID ile okur
   * @param collection Collection adı
   * @param id Document ID
   * @returns Document verisi veya null
   */
  read<T>(collection: string, id: string): Promise<T | null>;

  /**
   * Document'i günceller
   * @param collection Collection adı
   * @param id Document ID
   * @param data Güncellenecek veriler
   */
  update<T>(collection: string, id: string, data: Partial<T>): Promise<void>;

  /**
   * Document'i siler
   * @param collection Collection adı
   * @param id Document ID
   */
  delete(collection: string, id: string): Promise<void>;

  // ===========================================
  // QUERY VE LİSTELEME
  // ===========================================

  /**
   * Collection'daki tüm document'leri listeler
   * @param collection Collection adı
   * @param options Query seçenekleri
   * @returns Document listesi
   */
  list<T>(collection: string, options?: QueryOptions): Promise<T[]>;

  /**
   * Query ile document arar
   * @param collection Collection adı
   * @param options Query seçenekleri
   * @returns Document listesi
   */
  query<T>(collection: string, options: QueryOptions): Promise<T[]>;

  /**
   * Tek document arar (ilk eşleşen)
   * @param collection Collection adı
   * @param options Query seçenekleri
   * @returns Document verisi veya null
   */
  findOne<T>(collection: string, options: QueryOptions): Promise<T | null>;

  /**
   * Document sayısını döndürür
   * @param collection Collection adı
   * @param options Query seçenekleri
   * @returns Document sayısı
   */
  count(collection: string, options?: QueryOptions): Promise<number>;

  // ===========================================
  // BATCH İŞLEMLER
  // ===========================================

  /**
   * Birden fazla işlemi batch olarak yapar
   * @param operations Batch işlemler
   */
  batch(operations: BatchOperation[]): Promise<void>;

  /**
   * Birden fazla document oluşturur
   * @param collection Collection adı
   * @param data Document listesi
   * @returns Document ID listesi
   */
  createMany<T>(collection: string, data: T[]): Promise<string[]>;

  /**
   * Birden fazla document günceller
   * @param collection Collection adı
   * @param updates Güncelleme listesi
   */
  updateMany<T>(
    collection: string,
    updates: Array<{ id: string; data: Partial<T> }>,
  ): Promise<void>;

  /**
   * Birden fazla document siler
   * @param collection Collection adı
   * @param ids Document ID listesi
   */
  deleteMany(collection: string, ids: string[]): Promise<void>;

  // ===========================================
  // REAL-TIME SUBSCRIPTIONS
  // ===========================================

  /**
   * Collection'a real-time subscription oluşturur
   * @param collection Collection adı
   * @param callback Değişiklik callback'i
   * @param options Query seçenekleri
   * @returns Unsubscribe fonksiyonu
   */
  subscribe<T>(
    collection: string,
    callback: SubscriptionCallback<T>,
    options?: QueryOptions,
  ): UnsubscribeFunction;

  /**
   * Tek document'e real-time subscription oluşturur
   * @param collection Collection adı
   * @param id Document ID
   * @param callback Değişiklik callback'i
   * @returns Unsubscribe fonksiyonu
   */
  subscribeToDocument<T>(
    collection: string,
    id: string,
    callback: (data: T | null) => void,
  ): UnsubscribeFunction;

  // ===========================================
  // TRANSACTION DESTEĞI
  // ===========================================

  /**
   * Transaction çalıştırır
   * @param callback Transaction callback'i
   * @returns Transaction sonucu
   */
  runTransaction<T>(callback: TransactionCallback<T>): Promise<T>;

  // ===========================================
  // UTILITY VE METADATA
  // ===========================================

  /**
   * Veri kaynağı türünü döndürür
   * @returns Data source type
   */
  getDataSourceType(): DataSourceType;

  /**
   * Connection durumunu kontrol eder
   * @returns Connection aktif mi
   */
  isConnected(): Promise<boolean>;

  /**
   * Connection'ı başlatır
   */
  connect(): Promise<void>;

  /**
   * Connection'ı kapatır
   */
  disconnect(): Promise<void>;

  /**
   * Health check yapar
   * @returns Health status
   */
  healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    message?: string;
    timestamp: string;
  }>;
}

/**
 * Data Service Error türleri
 */
export class DataServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: unknown,
  ) {
    super(message);
    this.name = 'DataServiceError';
  }
}

/**
 * Yaygın hata kodları
 */
export const DATA_SERVICE_ERROR_CODES = {
  // Connection errors
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',

  // Document errors
  DOCUMENT_NOT_FOUND: 'DOCUMENT_NOT_FOUND',
  DOCUMENT_ALREADY_EXISTS: 'DOCUMENT_ALREADY_EXISTS',
  INVALID_DOCUMENT_ID: 'INVALID_DOCUMENT_ID',

  // Query errors
  INVALID_QUERY: 'INVALID_QUERY',
  QUERY_TIMEOUT: 'QUERY_TIMEOUT',

  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',

  // Permission errors
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',

  // Generic errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
} as const;
