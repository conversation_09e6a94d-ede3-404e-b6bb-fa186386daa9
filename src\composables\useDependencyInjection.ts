/**
 * Dependency Injection Composable
 *
 * Bu composable, Vue 3'ün provide/inject API'sini
 * tip güvenli ve kullanımı kolay hale getirir.
 */

import { inject, type InjectionKey } from 'vue';
import {
  configServiceKey,
  loggerKey,
  dataServiceKey,
  notificationServiceKey,
  themeServiceKey,
} from 'src/constants/keys';

/**
 * Tip güvenli inject fonksiyonu
 * Servisin mevcut olduğunu garanti eder
 *
 * @param key - InjectionKey
 * @param errorMessage - Hata mesajı (opsiyonel)
 * @returns Inject edilen servis
 * @throws Error - Servis bulunamazsa
 */
export function injectStrict<T>(key: InjectionKey<T>, errorMessage?: string): T {
  const service = inject(key);

  if (service === undefined) {
    const keyName = key.description || 'Unknown Service';
    const message =
      errorMessage ||
      `${keyName} is not provided. Make sure it's provided in a parent component or boot file.`;
    throw new Error(message);
  }

  return service;
}

/**
 * Opsiyonel inject fonksiyonu
 * Servis mevcut değilse undefined döner
 *
 * @param key - InjectionKey
 * @param defaultValue - Varsayılan değer
 * @returns Inject edilen servis veya varsayılan değer
 */
export function injectOptional<T>(key: InjectionKey<T>, defaultValue?: T): T | undefined {
  return inject(key, defaultValue);
}

/**
 * ConfigService'i inject eder
 *
 * @returns ConfigService instance
 * @throws Error - ConfigService provide edilmemişse
 */
export function useConfigService() {
  return injectStrict(
    configServiceKey,
    'ConfigService is required but not provided. Make sure config.ts boot file is loaded.',
  );
}

/**
 * Logger'ı inject eder
 *
 * @returns Logger instance
 * @throws Error - Logger provide edilmemişse
 */
export function useLogger() {
  return injectStrict(
    loggerKey,
    'Logger is required but not provided. Make sure logger.ts boot file is loaded.',
  );
}

/**
 * DataService'i inject eder
 *
 * @returns DataService instance
 * @throws Error - DataService provide edilmemişse
 */
export function useDataService() {
  return injectStrict(
    dataServiceKey,
    'DataService is required but not provided. Make sure data-service.ts boot file is loaded.',
  );
}

/**
 * NotificationService'i inject eder
 *
 * @returns NotificationService instance
 * @throws Error - NotificationService provide edilmemişse
 */
export function useNotificationService() {
  return injectStrict(
    notificationServiceKey,
    'NotificationService is required but not provided. Make sure notification service is configured.',
  );
}

/**
 * ThemeService'i inject eder
 *
 * @returns ThemeService instance
 * @throws Error - ThemeService provide edilmemişse
 */
export function useThemeService() {
  return injectStrict(
    themeServiceKey,
    'ThemeService is required but not provided. Make sure theme.ts boot file is loaded.',
  );
}

/**
 * Tüm çekirdek servisleri inject eder
 * Sadece gerekli olan yerlerde kullanılmalı
 *
 * @returns Tüm çekirdek servisler
 */
export function useCoreServices() {
  return {
    config: useConfigService(),
    logger: useLogger(),
    dataService: useDataService(),
    notificationService: useNotificationService(),
    themeService: useThemeService(),
  };
}

/**
 * Conditional injection
 * Servis mevcut olup olmadığını kontrol eder
 *
 * @param key - InjectionKey
 * @returns Servisin mevcut olup olmadığı ve servis
 */
export function useConditionalService<T>(key: InjectionKey<T>) {
  const service = inject(key);

  return {
    isAvailable: service !== undefined,
    service,
  };
}

/**
 * Development mode'da debug bilgisi sağlar
 * Hangi servislerin inject edildiğini gösterir
 */
export function useServiceDebugInfo() {
  if (import.meta.env.DEV) {
    const services = {
      configService: injectOptional(configServiceKey),
      logger: injectOptional(loggerKey),
      dataService: injectOptional(dataServiceKey),
      notificationService: injectOptional(notificationServiceKey),
      themeService: injectOptional(themeServiceKey),
    };

    const availableServices = Object.entries(services)
      .filter(([, service]) => service !== undefined)
      .map(([name]) => name);

    const unavailableServices = Object.entries(services)
      .filter(([, service]) => service === undefined)
      .map(([name]) => name);

    console.group('🔧 Dependency Injection Debug Info');
    console.log('✅ Available Services:', availableServices);
    if (unavailableServices.length > 0) {
      console.warn('❌ Unavailable Services:', unavailableServices);
    }
    console.groupEnd();

    return {
      availableServices,
      unavailableServices,
      totalServices: Object.keys(services).length,
    };
  }

  return null;
}

/**
 * Service health check
 * Tüm kritik servislerin mevcut olup olmadığını kontrol eder
 */
export function useServiceHealthCheck() {
  const healthCheck = () => {
    const criticalServices = [
      { name: 'ConfigService', key: configServiceKey },
      { name: 'Logger', key: loggerKey },
    ];

    const results = criticalServices.map(({ name, key }) => {
      const service = inject(key);
      return {
        name,
        isHealthy: service !== undefined,
        service,
      };
    });

    const allHealthy = results.every((result) => result.isHealthy);
    const unhealthyServices = results
      .filter((result) => !result.isHealthy)
      .map((result) => result.name);

    return {
      allHealthy,
      unhealthyServices,
      results,
    };
  };

  return {
    checkHealth: healthCheck,
  };
}

/**
 * Lazy service injection
 * Servisi sadece gerektiğinde inject eder
 */
export function useLazyService<T>(key: InjectionKey<T>) {
  let cachedService: T | undefined;

  const getService = (): T => {
    if (cachedService === undefined) {
      cachedService = injectStrict(key);
    }
    return cachedService;
  };

  return {
    getService,
    isLoaded: () => cachedService !== undefined,
  };
}

// Type exports for better TypeScript support
export type ServiceInjector<T> = () => T;
export type OptionalServiceInjector<T> = () => T | undefined;
export type ServiceHealthResult = {
  name: string;
  isHealthy: boolean;
  service: unknown;
};

/**
 * Custom hook factory
 * Yeni servisler için custom hook'lar oluşturmak için
 */
export function createServiceHook<T>(
  key: InjectionKey<T>,
  serviceName: string,
): ServiceInjector<T> {
  return () =>
    injectStrict(
      key,
      `${serviceName} is required but not provided. Make sure it's properly configured.`,
    );
}
