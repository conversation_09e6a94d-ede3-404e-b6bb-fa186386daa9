# Constants Dizini

Bu dizin **uygulama geneli sabitleri** içerir.

## Amaç

- Uygulama genelinde kullanılan sabit değerler
- Dependency Injection için `InjectionKey`'ler
- Enum'lar ve union type'lar
- Konfigürasyon sabitleri

## Dosya <PERSON>

- `keys.ts` - Vue 3 `provide`/`inject` için `InjectionKey`'ler
- `enums.ts` - Uygulama geneli enum'lar
- `config.ts` - Konfigürasyon sabitleri
- `api.ts` - API endpoint'leri ve HTTP status kodları

## Örnekler

### keys.ts

```typescript
import { InjectionKey } from 'vue';
import { IConfigService } from 'src/core/config/IConfigService';

export const configServiceKey: InjectionKey<IConfigService> = Symbol('ConfigService');
```

### enums.ts

```typescript
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest',
}

export enum NotificationLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  SUCCESS = 'success',
}
```

## Kurallar

- Sabitler UPPER_CASE ile yazılmalı
- Enum'lar PascalCase ile yazılmalı
- InjectionKey'ler camelCase ile yazılmalı
- TypeScript ile tip güvenliği sağlanmalı
