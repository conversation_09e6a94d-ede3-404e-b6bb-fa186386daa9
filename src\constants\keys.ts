/**
 * Vue 3 Dependency Injection Keys
 *
 * Bu dosya, provide/inject API'si için kullanılan InjectionKey'leri içerir.
 * Tip g<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> için her servis için ayrı key tanımlanır.
 */

import type { InjectionKey } from 'vue';

// Core servisler için key'ler
import type { IConfigService } from 'src/core/config/IConfigService';
// import { IDataService } from 'src/core/data-layer/IDataService';
// import { ILogger } from 'src/core/services/logger/ILogger';
// import { INotificationService } from 'src/core/services/notification/INotificationService';

// ConfigService artık tip güvenli
export const configServiceKey: InjectionKey<IConfigService> = Symbol('ConfigService');
export const dataServiceKey: InjectionKey<unknown> = Symbol('DataService');
export const loggerKey: InjectionKey<unknown> = Symbol('Logger');
export const notificationServiceKey: InjectionKey<unknown> = Symbol('NotificationService');

// Tema servisi için key
export const themeServiceKey: InjectionKey<unknown> = Symbol('ThemeService');

// Gelecekte eklenebilecek servisler için örnekler:
// export const authServiceKey: InjectionKey<IAuthService> = Symbol('AuthService');
// export const cacheServiceKey: InjectionKey<ICacheService> = Symbol('CacheService');
// export const analyticsServiceKey: InjectionKey<IAnalyticsService> = Symbol('AnalyticsService');

/**
 * Key'lerin kullanımı:
 *
 * Boot dosyasında provide etme:
 * app.provide(configServiceKey, configServiceInstance);
 *
 * Bileşen/composable'da inject etme:
 * const configService = inject(configServiceKey);
 *
 * Tip güvenliği için:
 * const configService = inject(configServiceKey)!; // Non-null assertion
 * // veya
 * const configService = inject(configServiceKey, defaultValue);
 */
