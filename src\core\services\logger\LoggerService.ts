/**
 * Logger Service Implementation
 * 
 * ILogger interface'ini implemente eden concrete sınıf.
 * ConfigService'i dependency injection ile alır ve
 * log seviyesine göre filtreleme yapar.
 * 
 * Özellikler:
 * - ConfigService entegrasyonu
 * - Seviye bazlı filtreleme
 * - Console ve gelecekte remote logging
 * - Performance ölçümü
 * - Structured logging
 * - Context yönetimi
 */

import type { ILogger, LogEntry, LoggerOptions } from './ILogger';
import type { IConfigService, LogLevel } from 'src/core/config/IConfigService';
import { LOG_LEVEL_PRIORITY, LOG_LEVEL_COLORS, LOG_LEVEL_EMOJIS } from './ILogger';

/**
 * Performance timer için veri yapısı
 */
interface PerformanceTimer {
  label: string;
  startTime: number;
  context?: Record<string, unknown>;
}

/**
 * Logger Service Implementation
 * 
 * SOLID prensiplerine uygun olarak:
 * - Single Responsibility: Sadece loglama işlemlerinden sorumlu
 * - Open/Closed: Yeni log output'ları eklenebilir
 * - Liskov Substitution: ILogger'ı tam olarak implemente eder
 * - Interface Segregation: Sadece gerekli metodları içerir
 * - Dependency Inversion: IConfigService abstraction'ına bağımlı
 */
export class LoggerService implements ILogger {
  private readonly configService: IConfigService;
  private defaultContext: Record<string, unknown> = {};
  private timers: Map<string, PerformanceTimer> = new Map();
  private groupLevel = 0;
  private options: LoggerOptions = {
    format: 'text',
    enableConsole: true,
    enableRemote: false,
  };

  /**
   * Constructor - Dependency Injection
   * @param configService Configuration service instance
   */
  constructor(configService: IConfigService) {
    this.configService = configService;
    this.initializeLogger();
  }

  // ===========================================
  // TEMEL LOG METODLARI
  // ===========================================

  error(message: string, context?: Record<string, unknown>, error?: Error): void {
    this.logWithLevel('error', message, context, error);
  }

  warn(message: string, context?: Record<string, unknown>): void {
    this.logWithLevel('warn', message, context);
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.logWithLevel('info', message, context);
  }

  debug(message: string, context?: Record<string, unknown>): void {
    this.logWithLevel('debug', message, context);
  }

  trace(message: string, context?: Record<string, unknown>): void {
    this.logWithLevel('trace', message, context);
  }

  // ===========================================
  // STRUCTURED LOGGING METODLARI
  // ===========================================

  log(entry: LogEntry): void {
    if (!this.isLevelEnabled(entry.level)) {
      return;
    }

    this.writeLog(entry);
  }

  logWithLevel(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): void {
    if (!this.isLevelEnabled(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: { ...this.defaultContext, ...context },
      error,
    };

    this.writeLog(entry);
  }

  // ===========================================
  // CONTEXT YÖNETİMİ
  // ===========================================

  setDefaultContext(context: Record<string, unknown>): void {
    this.defaultContext = { ...context };
  }

  getDefaultContext(): Record<string, unknown> {
    return { ...this.defaultContext };
  }

  clearDefaultContext(): void {
    this.defaultContext = {};
  }

  // ===========================================
  // KONFIGÜRASYON VE KONTROL
  // ===========================================

  isLevelEnabled(level: LogLevel): boolean {
    const currentLevel = this.configService.getLogLevel();
    return LOG_LEVEL_PRIORITY[level] <= LOG_LEVEL_PRIORITY[currentLevel];
  }

  getMinLevel(): LogLevel {
    return this.configService.getLogLevel();
  }

  configure(options: Partial<LoggerOptions>): void {
    this.options = { ...this.options, ...options };
  }

  // ===========================================
  // PERFORMANCE VE UTILITY
  // ===========================================

  startTimer(label: string): string {
    const timerId = `${label}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timer: PerformanceTimer = {
      label,
      startTime: performance.now(),
    };
    
    this.timers.set(timerId, timer);
    this.debug(`Timer started: ${label}`, { timerId });
    
    return timerId;
  }

  endTimer(timerId: string, context?: Record<string, unknown>): void {
    const timer = this.timers.get(timerId);
    if (!timer) {
      this.warn(`Timer not found: ${timerId}`);
      return;
    }

    const duration = performance.now() - timer.startTime;
    this.timers.delete(timerId);

    this.info(`Timer ended: ${timer.label}`, {
      timerId,
      duration: `${duration.toFixed(2)}ms`,
      ...context,
    });
  }

  time<T>(label: string, fn: () => T): T {
    const timerId = this.startTimer(label);
    try {
      const result = fn();
      this.endTimer(timerId, { success: true });
      return result;
    } catch (error) {
      this.endTimer(timerId, { success: false, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async timeAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    const timerId = this.startTimer(label);
    try {
      const result = await fn();
      this.endTimer(timerId, { success: true });
      return result;
    } catch (error) {
      this.endTimer(timerId, { success: false, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  // ===========================================
  // GRUP VE BATCH LOGGING
  // ===========================================

  group(label: string, collapsed = false): void {
    if (this.options.enableConsole) {
      if (collapsed) {
        console.groupCollapsed(`${LOG_LEVEL_EMOJIS.info} ${label}`);
      } else {
        console.group(`${LOG_LEVEL_EMOJIS.info} ${label}`);
      }
    }
    this.groupLevel++;
  }

  groupEnd(): void {
    if (this.groupLevel > 0) {
      if (this.options.enableConsole) {
        console.groupEnd();
      }
      this.groupLevel--;
    }
  }

  batch(entries: LogEntry[]): void {
    entries.forEach(entry => this.log(entry));
  }

  // ===========================================
  // PRIVATE METODLAR
  // ===========================================

  /**
   * Logger'ı başlatır ve varsayılan ayarları yapar
   */
  private initializeLogger(): void {
    // Development modunda daha detaylı loglama
    if (this.configService.isDevelopment()) {
      this.setDefaultContext({
        environment: 'development',
        appName: this.configService.getAppName(),
        appVersion: this.configService.getAppVersion(),
      });
    }

    // Production modunda minimal context
    if (this.configService.isProduction()) {
      this.setDefaultContext({
        environment: 'production',
        appVersion: this.configService.getAppVersion(),
      });
    }

    this.debug('Logger initialized', {
      minLevel: this.getMinLevel(),
      enableConsole: this.options.enableConsole,
      enableRemote: this.options.enableRemote,
    });
  }

  /**
   * Log entry'yi gerçek output'lara yazdırır
   */
  private writeLog(entry: LogEntry): void {
    // Console'a yazdırma
    if (this.options.enableConsole) {
      this.writeToConsole(entry);
    }

    // Gelecekte remote logging burada eklenebilir
    if (this.options.enableRemote) {
      this.writeToRemote(entry);
    }
  }

  /**
   * Console'a log yazdırır
   */
  private writeToConsole(entry: LogEntry): void {
    const { level, message, timestamp, context, error } = entry;
    const emoji = LOG_LEVEL_EMOJIS[level];
    const color = LOG_LEVEL_COLORS[level];
    
    // Grup indentasyonu
    const indent = '  '.repeat(this.groupLevel);
    
    if (this.options.format === 'json') {
      console.log(JSON.stringify(entry, null, 2));
      return;
    }

    // Text format
    const timeStr = new Date(timestamp).toLocaleTimeString();
    const levelStr = level.toUpperCase().padEnd(5);
    
    const logMessage = `${indent}${emoji} [${timeStr}] ${levelStr} ${message}`;
    
    // Console metodunu seviyeye göre seç
    const consoleMethod = this.getConsoleMethod(level);
    
    if (context && Object.keys(context).length > 0) {
      consoleMethod(`%c${logMessage}`, `color: ${color}`, context);
    } else {
      consoleMethod(`%c${logMessage}`, `color: ${color}`);
    }

    // Hata varsa ayrıca yazdır
    if (error) {
      console.error(error);
    }
  }

  /**
   * Remote logging (gelecekte implementasyon)
   */
  private writeToRemote(entry: LogEntry): void {
    // TODO: Remote logging implementasyonu
    // Örnek: API'ye gönderme, local storage'a kaydetme vb.
    console.debug('Remote logging not implemented yet', entry);
  }

  /**
   * Log seviyesine göre uygun console metodunu döndürür
   */
  private getConsoleMethod(level: LogLevel): typeof console.log {
    switch (level) {
      case 'error':
        return console.error;
      case 'warn':
        return console.warn;
      case 'info':
        return console.info;
      case 'debug':
        return console.debug;
      case 'trace':
        return console.trace;
      default:
        return console.log;
    }
  }
}
