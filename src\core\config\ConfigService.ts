/**
 * Configuration Service Implementation
 *
 * Bu sınıf IConfigService interface'ini implemente eder.
 * Ortam değişkenlerini okur, işler ve tip güvenli şekilde sunar.
 *
 * Sorumluluklar:
 * - import.meta.env'den ham değerleri okuma
 * - String değerleri uygun tiplere çevirme
 * - Varsayılan değerleri uygulama
 * - Konfigürasyon validasyonu
 */

import type {
  IConfigService,
  DataSourceType,
  LogLevel,
  ThemeType,
  NotificationLevel,
  ApiErrorNotificationType,
  QNotifyPosition,
  FirebaseConfig,
} from './IConfigService';

/**
 * ConfigService implementasyonu
 *
 * SOLID Prensiplerine uygun:
 * - Single Responsibility: Sadece konfigürasyon yönetimi
 * - Open/Closed: Interface ile genişlemeye açık
 * - Dependency Inversion: Interface'e bağlı
 */
export class ConfigService implements IConfigService {
  private readonly env: ImportMetaEnv;
  private validationErrors: string[] = [];

  constructor() {
    this.env = import.meta.env;
    this.validateConfiguration();
  }

  // ===========================================
  // UYGULAMA GENEL AYARLARI
  // ===========================================

  getAppName(): string {
    return this.env.VITE_APP_NAME || 'Quasar App';
  }

  getAppVersion(): string {
    return this.env.VITE_APP_VERSION || '1.0.0';
  }

  // ===========================================
  // VERİ KAYNAĞI AYARLARI
  // ===========================================

  getDataSourceType(): DataSourceType {
    const dataSource = this.env.VITE_DATA_SOURCE_TYPE;

    if (dataSource === 'firebase' || dataSource === 'restapi' || dataSource === 'mock') {
      return dataSource;
    }

    // Varsayılan değer
    return 'mock';
  }

  // ===========================================
  // REST API AYARLARI
  // ===========================================

  getApiBaseUrl(): string | undefined {
    const dataSource = this.getDataSourceType();
    if (dataSource !== 'restapi') {
      return undefined;
    }

    return this.env.VITE_API_BASE_URL;
  }

  getApiTimeout(): number | undefined {
    const dataSource = this.getDataSourceType();
    if (dataSource !== 'restapi') {
      return undefined;
    }

    const timeout = this.env.VITE_API_TIMEOUT;
    if (!timeout) {
      return 30000; // 30 saniye varsayılan
    }

    const parsed = parseInt(timeout, 10);
    return isNaN(parsed) ? 30000 : parsed;
  }

  // ===========================================
  // FIREBASE AYARLARI
  // ===========================================

  getFirebaseConfig(): FirebaseConfig | undefined {
    const dataSource = this.getDataSourceType();
    if (dataSource !== 'firebase') {
      return undefined;
    }

    const {
      VITE_FIREBASE_API_KEY,
      VITE_FIREBASE_AUTH_DOMAIN,
      VITE_FIREBASE_PROJECT_ID,
      VITE_FIREBASE_STORAGE_BUCKET,
      VITE_FIREBASE_MESSAGING_SENDER_ID,
      VITE_FIREBASE_APP_ID,
      VITE_FIREBASE_MEASUREMENT_ID,
    } = this.env;

    // Zorunlu alanları kontrol et
    if (
      !VITE_FIREBASE_API_KEY ||
      !VITE_FIREBASE_AUTH_DOMAIN ||
      !VITE_FIREBASE_PROJECT_ID ||
      !VITE_FIREBASE_STORAGE_BUCKET ||
      !VITE_FIREBASE_MESSAGING_SENDER_ID ||
      !VITE_FIREBASE_APP_ID
    ) {
      return undefined;
    }

    const config: FirebaseConfig = {
      apiKey: VITE_FIREBASE_API_KEY,
      authDomain: VITE_FIREBASE_AUTH_DOMAIN,
      projectId: VITE_FIREBASE_PROJECT_ID,
      storageBucket: VITE_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: VITE_FIREBASE_MESSAGING_SENDER_ID,
      appId: VITE_FIREBASE_APP_ID,
    };

    // measurementId opsiyonel, sadece varsa ekle
    if (VITE_FIREBASE_MEASUREMENT_ID) {
      config.measurementId = VITE_FIREBASE_MEASUREMENT_ID;
    }

    return config;
  }

  // ===========================================
  // ÇOKLU DİL (i18n) AYARLARI
  // ===========================================

  getDefaultLocale(): string {
    return this.env.VITE_DEFAULT_LOCALE || 'tr-TR';
  }

  getFallbackLocale(): string {
    return this.env.VITE_FALLBACK_LOCALE || 'en-US';
  }

  // ===========================================
  // TEMA AYARLARI
  // ===========================================

  getDefaultTheme(): ThemeType {
    const theme = this.env.VITE_DEFAULT_THEME;

    if (theme === 'light' || theme === 'dark' || theme === 'auto') {
      return theme;
    }

    return 'light'; // Varsayılan
  }

  // ===========================================
  // LOGLAMA AYARLARI
  // ===========================================

  getLogLevel(): LogLevel {
    const level = this.env.VITE_LOG_LEVEL;

    if (
      level === 'error' ||
      level === 'warn' ||
      level === 'info' ||
      level === 'debug' ||
      level === 'trace'
    ) {
      return level;
    }

    // Development'ta debug, production'da info
    return this.isDevelopment() ? 'debug' : 'info';
  }

  // ===========================================
  // NOTİFİKASYON AYARLARI
  // ===========================================

  getNotificationDisplayLevel(): NotificationLevel {
    const level = this.env.VITE_NOTIFICATION_DISPLAY_LEVEL;

    if (
      level === 'error' ||
      level === 'warn' ||
      level === 'info' ||
      level === 'success' ||
      level === 'none'
    ) {
      return level;
    }

    return 'info'; // Varsayılan
  }

  getApiErrorNotificationType(): ApiErrorNotificationType {
    const type = this.env.VITE_API_ERROR_NOTIFICATION_TYPE;

    if (type === 'toast' || type === 'friendly' || type === 'silent') {
      return type;
    }

    return 'friendly'; // Varsayılan
  }

  getNotificationDefaultPosition(): QNotifyPosition {
    const position = this.env.VITE_NOTIFICATION_DEFAULT_POSITION;

    const validPositions: QNotifyPosition[] = [
      'top-left',
      'top',
      'top-right',
      'left',
      'center',
      'right',
      'bottom-left',
      'bottom',
      'bottom-right',
    ];

    if (validPositions.includes(position as QNotifyPosition)) {
      return position as QNotifyPosition;
    }

    return 'top-right'; // Varsayılan
  }

  getNotificationDefaultTimeout(): number {
    const timeout = this.env.VITE_NOTIFICATION_DEFAULT_TIMEOUT;

    if (!timeout) {
      return 5000; // 5 saniye varsayılan
    }

    const parsed = parseInt(timeout, 10);
    return isNaN(parsed) ? 5000 : parsed;
  }

  getNotificationDefaultGroup(): boolean {
    const group = this.env.VITE_NOTIFICATION_DEFAULT_GROUP;

    if (group === 'true') return true;
    if (group === 'false') return false;

    return true; // Varsayılan
  }

  getNotificationDefaultCloseBtnLabel(): string | boolean {
    const label = this.env.VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL;

    if (!label || label === '') {
      return false; // Quasar varsayılanını kullan
    }

    return label;
  }

  // ===========================================
  // ENVIRONMENT KONTROL METODLARI
  // ===========================================

  isDevelopment(): boolean {
    return this.env.DEV === true;
  }

  isProduction(): boolean {
    return this.env.PROD === true;
  }

  // ===========================================
  // VALIDATION VE UTILITY METODLARI
  // ===========================================

  isValid(): boolean {
    return this.validationErrors.length === 0;
  }

  getValidationErrors(): string[] {
    return [...this.validationErrors];
  }

  getConfigSummary(): Record<string, unknown> {
    return {
      app: {
        name: this.getAppName(),
        version: this.getAppVersion(),
        environment: this.isDevelopment() ? 'development' : 'production',
      },
      dataSource: {
        type: this.getDataSourceType(),
        apiBaseUrl: this.getApiBaseUrl(),
        apiTimeout: this.getApiTimeout(),
        hasFirebaseConfig: !!this.getFirebaseConfig(),
      },
      i18n: {
        defaultLocale: this.getDefaultLocale(),
        fallbackLocale: this.getFallbackLocale(),
      },
      theme: {
        defaultTheme: this.getDefaultTheme(),
      },
      logging: {
        level: this.getLogLevel(),
      },
      notifications: {
        displayLevel: this.getNotificationDisplayLevel(),
        apiErrorType: this.getApiErrorNotificationType(),
        position: this.getNotificationDefaultPosition(),
        timeout: this.getNotificationDefaultTimeout(),
        group: this.getNotificationDefaultGroup(),
        closeBtnLabel: this.getNotificationDefaultCloseBtnLabel(),
      },
      validation: {
        isValid: this.isValid(),
        errors: this.getValidationErrors(),
      },
    };
  }

  // ===========================================
  // PRIVATE METODLAR
  // ===========================================

  /**
   * Konfigürasyonu validate eder
   * Kritik hatalar varsa validationErrors array'ine ekler
   */
  private validateConfiguration(): void {
    this.validationErrors = [];

    // Uygulama adı kontrolü
    if (!this.env.VITE_APP_NAME) {
      this.validationErrors.push('VITE_APP_NAME is required');
    }

    // Veri kaynağı kontrolü
    const dataSource = this.env.VITE_DATA_SOURCE_TYPE;
    if (!dataSource) {
      this.validationErrors.push('VITE_DATA_SOURCE_TYPE is required');
    } else if (!['firebase', 'restapi', 'mock'].includes(dataSource)) {
      this.validationErrors.push('VITE_DATA_SOURCE_TYPE must be one of: firebase, restapi, mock');
    }

    // REST API kontrolü
    if (dataSource === 'restapi' && !this.env.VITE_API_BASE_URL) {
      this.validationErrors.push('VITE_API_BASE_URL is required when using REST API');
    }

    // Firebase kontrolü
    if (dataSource === 'firebase') {
      const requiredFirebaseFields = [
        'VITE_FIREBASE_API_KEY',
        'VITE_FIREBASE_AUTH_DOMAIN',
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_FIREBASE_STORAGE_BUCKET',
        'VITE_FIREBASE_MESSAGING_SENDER_ID',
        'VITE_FIREBASE_APP_ID',
      ];

      for (const field of requiredFirebaseFields) {
        if (!this.env[field as keyof ImportMetaEnv]) {
          this.validationErrors.push(`${field} is required when using Firebase`);
        }
      }
    }

    // Development modda validation hatalarını console'a yazdır
    if (this.isDevelopment() && this.validationErrors.length > 0) {
      console.group('🔧 ConfigService Validation Errors');
      this.validationErrors.forEach((error) => console.error('❌', error));
      console.groupEnd();
    }
  }
}
