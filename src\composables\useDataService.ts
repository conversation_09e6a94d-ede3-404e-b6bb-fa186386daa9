/**
 * Data Service Composable
 * 
 * Bu composable, Vue bileşenlerinde DataService'i kullanmak için
 * kolay bir interface sağlar. Dependency injection ile DataService'i
 * alır ve tip güvenli bir şekilde sunar.
 * 
 * Özellikler:
 * - Tip güvenli data service erişimi
 * - Hata yönetimi
 * - <PERSON><PERSON> kullanım
 * - Vue 3 Composition API uyumlu
 * - Logger entegrasyonu
 */

import { inject } from 'vue';
import type { IDataService } from 'src/core/data-layer/IDataService';
import type { ILogger } from 'src/core/services/logger/ILogger';
import { dataServiceKey, loggerKey } from 'src/constants/keys';

/**
 * Data Service Composable
 * 
 * Vue bileşenlerinde data service kullanımı için composable.
 * 
 * @returns DataService instance
 * @throws Error DataService bulunamadığında
 * 
 * @example
 * ```typescript
 * // Bileşende kullanım
 * import { useDataService } from 'src/composables/useDataService';
 * 
 * export default defineComponent({
 *   setup() {
 *     const dataService = useDataService();
 *     
 *     const loadUsers = async () => {
 *       try {
 *         const users = await dataService.list('users');
 *         return users;
 *       } catch (error) {
 *         console.error('Failed to load users:', error);
 *         throw error;
 *       }
 *     };
 *     
 *     return {
 *       loadUsers
 *     };
 *   }
 * });
 * ```
 */
export function useDataService(): IDataService {
  const dataService = inject<IDataService>(dataServiceKey);
  
  if (!dataService) {
    // DataService bulunamadı - bu kritik bir hata
    const errorMessage = 'DataService not found! Make sure firebase.ts (or appropriate data service) boot file is properly configured.';
    console.error('❌ [useDataService]', errorMessage);
    throw new Error(errorMessage);
  }
  
  return dataService;
}

/**
 * Safe Data Service Composable
 * 
 * DataService bulunamadığında hata fırlatmak yerine fallback service döndürür.
 * Bu, data service olmadan da çalışması gereken bileşenler için kullanılabilir.
 * 
 * @returns DataService instance veya fallback service
 * 
 * @example
 * ```typescript
 * // Güvenli kullanım
 * import { useSafeDataService } from 'src/composables/useDataService';
 * 
 * export default defineComponent({
 *   setup() {
 *     const dataService = useSafeDataService();
 *     
 *     // DataService yoksa bile çalışır (fallback ile)
 *     const users = await dataService.list('users'); // Hata fırlatır ama uygulama çökmez
 *     
 *     return {};
 *   }
 * });
 * ```
 */
export function useSafeDataService(): IDataService {
  const dataService = inject<IDataService>(dataServiceKey);
  
  if (!dataService) {
    // Fallback data service döndür
    console.warn('⚠️ [useSafeDataService] DataService not found, using fallback service');
    
    return createFallbackDataService();
  }
  
  return dataService;
}

/**
 * Optional Data Service Composable
 * 
 * DataService'in mevcut olup olmadığını kontrol eder ve optional olarak döndürür.
 * Bu, data service'in opsiyonel olduğu durumlar için kullanılır.
 * 
 * @returns DataService instance veya undefined
 * 
 * @example
 * ```typescript
 * // Opsiyonel kullanım
 * import { useOptionalDataService } from 'src/composables/useDataService';
 * 
 * export default defineComponent({
 *   setup() {
 *     const dataService = useOptionalDataService();
 *     
 *     if (dataService) {
 *       const users = await dataService.list('users');
 *     }
 *     
 *     return {};
 *   }
 * });
 * ```
 */
export function useOptionalDataService(): IDataService | undefined {
  return inject<IDataService>(dataServiceKey);
}

/**
 * Data Service with Logger Composable
 * 
 * DataService'i logger ile birlikte döndürür.
 * Otomatik error logging ve performance tracking için kullanılır.
 * 
 * @returns DataService ve Logger instance'ları
 * 
 * @example
 * ```typescript
 * // Logger ile kullanım
 * import { useDataServiceWithLogger } from 'src/composables/useDataService';
 * 
 * export default defineComponent({
 *   setup() {
 *     const { dataService, logger } = useDataServiceWithLogger();
 *     
 *     const loadUsers = async () => {
 *       const timerId = logger.startTimer('Load Users');
 *       try {
 *         const users = await dataService.list('users');
 *         logger.endTimer(timerId);
 *         logger.info('Users loaded successfully', { count: users.length });
 *         return users;
 *       } catch (error) {
 *         logger.endTimer(timerId);
 *         logger.error('Failed to load users', {}, error);
 *         throw error;
 *       }
 *     };
 *     
 *     return { loadUsers };
 *   }
 * });
 * ```
 */
export function useDataServiceWithLogger(): { dataService: IDataService; logger: ILogger | undefined } {
  const dataService = useDataService();
  const logger = inject<ILogger>(loggerKey);
  
  return { dataService, logger };
}

/**
 * Fallback data service oluşturur
 * DataService bulunamadığında kullanılır
 */
function createFallbackDataService(): IDataService {
  return {
    // Temel CRUD
    create: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    read: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    update: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    delete: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    
    // Query
    list: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    query: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    findOne: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    count: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    
    // Batch
    batch: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    createMany: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    updateMany: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    deleteMany: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    
    // Subscriptions
    subscribe: () => {
      throw new Error('DataService not available - using fallback service');
    },
    subscribeToDocument: () => {
      throw new Error('DataService not available - using fallback service');
    },
    
    // Transaction
    runTransaction: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    
    // Utility
    getDataSourceType: () => 'mock',
    isConnected: async () => false,
    connect: async () => {
      throw new Error('DataService not available - using fallback service');
    },
    disconnect: async () => {},
    healthCheck: async () => ({
      status: 'unhealthy' as const,
      message: 'DataService not available - using fallback service',
      timestamp: new Date().toISOString(),
    }),
  };
}

/**
 * Data Service Composable Kullanım Örnekleri:
 * 
 * 1. Temel CRUD Kullanımı:
 *    const dataService = useDataService();
 *    
 *    // Create
 *    const userId = await dataService.create('users', { name: 'John', email: '<EMAIL>' });
 *    
 *    // Read
 *    const user = await dataService.read('users', userId);
 *    
 *    // Update
 *    await dataService.update('users', userId, { name: 'John Doe' });
 *    
 *    // Delete
 *    await dataService.delete('users', userId);
 * 
 * 2. Query Kullanımı:
 *    const dataService = useDataService();
 *    
 *    // List all
 *    const allUsers = await dataService.list('users');
 *    
 *    // Query with filters
 *    const activeUsers = await dataService.query('users', {
 *      where: [{ field: 'active', operator: '==', value: true }],
 *      orderBy: [{ field: 'createdAt', direction: 'desc' }],
 *      limit: 10
 *    });
 *    
 *    // Find one
 *    const adminUser = await dataService.findOne('users', {
 *      where: [{ field: 'role', operator: '==', value: 'admin' }]
 *    });
 * 
 * 3. Real-time Subscriptions:
 *    const dataService = useDataService();
 *    
 *    const unsubscribe = dataService.subscribe('users', (users) => {
 *      console.log('Users updated:', users);
 *    }, {
 *      where: [{ field: 'active', operator: '==', value: true }]
 *    });
 *    
 *    // Cleanup
 *    onUnmounted(() => {
 *      unsubscribe();
 *    });
 * 
 * 4. Batch Operations:
 *    const dataService = useDataService();
 *    
 *    await dataService.batch([
 *      { type: 'create', collection: 'users', data: { name: 'User 1' } },
 *      { type: 'update', collection: 'users', id: 'user123', data: { active: false } },
 *      { type: 'delete', collection: 'users', id: 'user456' }
 *    ]);
 * 
 * 5. Transactions:
 *    const dataService = useDataService();
 *    
 *    await dataService.runTransaction(async (transaction) => {
 *      const user = await transaction.get('users', 'user123');
 *      if (user) {
 *        await transaction.update('users', 'user123', { lastLogin: new Date() });
 *        await transaction.create('logs', { userId: 'user123', action: 'login' });
 *      }
 *    });
 * 
 * 6. Error Handling:
 *    const dataService = useDataService();
 *    
 *    try {
 *      const users = await dataService.list('users');
 *    } catch (error) {
 *      if (error instanceof DataServiceError) {
 *        console.error('Data service error:', error.code, error.message);
 *      } else {
 *        console.error('Unknown error:', error);
 *      }
 *    }
 */
