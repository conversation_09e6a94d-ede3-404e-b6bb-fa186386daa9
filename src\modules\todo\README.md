# Todo Modülü

Bu modül **Todo List özelliğini** içerir.

## Amaç

- <PERSON><PERSON>kle<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, silme işlemleri
- Todo listesi görüntüleme
- Todo durumu yönetimi (tamamlandı/bekliyor)
- Modüler mimari örneği

## Dosya Yapısı

### `types/`

- `ITodo.ts` - Todo interface tanımı
- `TodoStatus.ts` - Todo durum enum'u

### `services/`

- `TodoService.ts` - Todo CRUD işlemleri

### `stores/`

- `todo.store.ts` - Todo state yönetimi (Pinia)

### `components/`

- `TodoList.vue` - Todo listesi bileşeni
- `TodoItem.vue` - Tek todo item bileşeni
- `AddTodoForm.vue` - Todo ekleme formu

### `pages/`

- `TodoPage.vue` - Ana todo sayfası

### `router/`

- `routes.ts` - Todo modülü rotaları

### `i18n/`

- `en-US.ts` - İngilizce çeviriler
- `tr-TR.ts` - Türkçe çeviriler

## Özellikler

- ✅ CRUD işlemleri
- ✅ State yönetimi
- ✅ Çoklu dil desteği
- ✅ TypeScript tip güvenliği
- ✅ Modüler yapı
- ✅ SOLID prensiplerine uygunluk

## Kullanım

Bu modül, modüler mimarinin nasıl uygulanacağına dair bir örnek teşkil eder.
