# Notification Servisi

Bu dokümantasyon, Notification Servisi'nin nasıl kullanılacağını ve yapılandırılacağını açıklar.

## Genel Bakış

Notification Servisi, Quasar Notify plugin'ini sarmalayarak ConfigService ile entegre çalışan, tip güvenli ve kolay kullanımlı notification sistemi sağlar.

### Özellikler

- ✅ **ConfigService Entegrasyonu**: .env dosyasından notification ayarları
- ✅ **Logger Entegrasyonu**: Notification'lar otomatik loglanır
- ✅ **Seviye Bazlı Filtreleme**: error, warn, info, success, none
- ✅ **API Error Handling**: Ak<PERSON>ll<PERSON> hata yönetimi (toast, friendly, silent)
- ✅ **Loading & Progress**: Spinner ve progress bar desteği
- ✅ **Tip Güvenliği**: Full TypeScript support
- ✅ **Fallback Mechanisms**: Hata durumunda çalışmaya devam etme
- ✅ **Customizable**: Position, timeout, actions vb. özelleştirilebilir

## Kurulum ve Yapılandırma

### 1. Ortam Değişkenleri

`.env.local` dosyasında notification ayarlarını yapılandırın:

```env
# Notification Display Level (hangi seviyedeki notification'lar gösterilsin)
VITE_NOTIFICATION_DISPLAY_LEVEL="info"  # error, warn, info, success, none

# API Error Notification Type
VITE_API_ERROR_NOTIFICATION_TYPE="friendly"  # toast, friendly, silent

# Notification Defaults
VITE_NOTIFICATION_DEFAULT_POSITION="top-right"
VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"
VITE_NOTIFICATION_DEFAULT_GROUP="true"
VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""
```

### 2. Boot Dosyaları

Notification sistemi otomatik olarak başlatılır:
- `src/boot/notify-defaults.ts` - Varsayılan ayarları yapılandırır
- `quasar.config.ts`'de boot sırası: `['config', 'logger', 'notify-defaults', ...]`

## Kullanım

### 1. Composable ile Kullanım (Önerilen)

```typescript
import { useNotify } from 'src/composables/useNotify';

export default defineComponent({
  setup() {
    const notify = useNotify();
    
    const handleSuccess = () => {
      notify.success('Operation completed successfully!');
    };
    
    const handleError = (error: Error) => {
      notify.apiError({
        error,
        friendlyMessage: 'Something went wrong. Please try again.'
      });
    };
    
    return {
      handleSuccess,
      handleError
    };
  }
});
```

### 2. Doğrudan Quasar Notify Kullanımı

```typescript
import { Notify } from 'quasar';

// Varsayılan ayarlar otomatik uygulanır
Notify.create({
  message: 'Hello World',
  type: 'positive'
});
```

## Notification Seviyeleri

### Seviye Hiyerarşisi

1. **error** (0) - Kritik hatalar, her zaman gösterilir
2. **warn** (1) - Uyarılar
3. **info** (2) - Genel bilgiler
4. **success** (3) - Başarı mesajları
5. **none** (4) - Hiçbir notification gösterilmez

### Seviye Filtreleme

`VITE_NOTIFICATION_DISPLAY_LEVEL` ayarına göre filtreleme yapılır:

- `error` → Sadece error notification'ları
- `warn` → error + warn notification'ları
- `info` → error + warn + info notification'ları
- `success` → Tüm notification'lar
- `none` → Hiçbir notification gösterilmez

## API Error Handling

### Error Types

```typescript
// 1. Toast - Teknik hata mesajını göster
VITE_API_ERROR_NOTIFICATION_TYPE="toast"

// 2. Friendly - Kullanıcı dostu mesajı göster
VITE_API_ERROR_NOTIFICATION_TYPE="friendly"

// 3. Silent - Notification gösterme, sadece logla
VITE_API_ERROR_NOTIFICATION_TYPE="silent"
```

### Kullanım Örneği

```typescript
const notify = useNotify();

try {
  await apiCall();
} catch (error) {
  notify.apiError({
    error,
    friendlyMessage: 'Failed to save data. Please try again.',
    context: { userId: 123, action: 'save' }
  });
}
```

## Kullanım Örnekleri

### Temel Notification'lar

```typescript
const notify = useNotify();

// Success
notify.success('Data saved successfully!');

// Info
notify.info('New message received');

// Warning
notify.warning('Please check your input');

// Error
notify.error('Failed to connect to server');
```

### Loading Notification

```typescript
const notify = useNotify();

const loadingNotification = notify.loading('Saving data...');

try {
  await saveData();
  loadingNotification(); // Kapat
  notify.success('Data saved successfully!');
} catch (error) {
  loadingNotification(); // Kapat
  notify.apiError({ error });
}
```

### Progress Notification

```typescript
const notify = useNotify();

for (let i = 0; i <= 100; i += 10) {
  notify.progress('Uploading file...', i);
  await delay(100);
}

notify.success('File uploaded successfully!');
```

### Custom Options

```typescript
const notify = useNotify();

notify.info('Custom notification', {
  timeout: 10000,
  position: 'bottom-left',
  actions: [
    {
      label: 'Undo',
      color: 'yellow',
      handler: () => undoAction()
    }
  ],
  context: { feature: 'customNotification' }
});
```

### Batch Notifications

```typescript
const notify = useNotify();

// Birden fazla notification'ı sırayla göster
const messages = [
  'Step 1 completed',
  'Step 2 completed', 
  'Step 3 completed'
];

messages.forEach((message, index) => {
  setTimeout(() => {
    notify.success(message);
  }, index * 1000);
});
```

## Güvenli Kullanım

### Safe Notify Composable

```typescript
import { useSafeNotify } from 'src/composables/useNotify';

// Hata durumunda bile çalışır
const notify = useSafeNotify();
notify.info('This will always work');
```

### Error Handling

```typescript
const notify = useNotify();

// Seviye kontrolü
if (notify.isLevelEnabled('debug')) {
  notify.info('Debug information');
}

// Try-catch ile güvenli kullanım
try {
  notify.success('Operation completed');
} catch (error) {
  console.error('Notification failed:', error);
  // Fallback davranış
}
```

## Best Practices

### 1. Anlamlı Mesajlar

```typescript
// ❌ Kötü
notify.error('Error');

// ✅ İyi
notify.error('Failed to save user profile. Please check your internet connection.');
```

### 2. Uygun Seviye Seçimi

```typescript
// ❌ Kötü - success seviyesinde hata
notify.success('Database connection failed');

// ✅ İyi - error seviyesinde hata
notify.error('Database connection failed');
```

### 3. API Error Handling

```typescript
// ❌ Kötü - teknik hata mesajı
notify.error(error.message);

// ✅ İyi - kullanıcı dostu mesaj
notify.apiError({
  error,
  friendlyMessage: 'Unable to save changes. Please try again.'
});
```

### 4. Loading States

```typescript
// ❌ Kötü - loading state yönetimi yok
await longRunningOperation();
notify.success('Completed');

// ✅ İyi - loading state ile
const loading = notify.loading('Processing...');
try {
  await longRunningOperation();
  loading(); // Kapat
  notify.success('Completed successfully');
} catch (error) {
  loading(); // Kapat
  notify.apiError({ error });
}
```

## Gelişmiş Özellikler

### Context Logging

```typescript
const notify = useNotify();

notify.success('User created', {
  userId: newUser.id,
  userEmail: newUser.email,
  timestamp: new Date().toISOString()
});
// Bu bilgiler otomatik olarak logger'a da yazılır
```

### Conditional Notifications

```typescript
const notify = useNotify();

// Sadece development modunda göster
if (configService.isDevelopment()) {
  notify.info('Debug: API response received');
}

// Seviye kontrolü ile
if (notify.isLevelEnabled('debug')) {
  notify.info('Debug information');
}
```

## Troubleshooting

### Notification Görünmüyor

**Çözüm:**
1. `.env.local` dosyasında `VITE_NOTIFICATION_DISPLAY_LEVEL` ayarını kontrol edin
2. `notify.isLevelEnabled('info')` ile seviye kontrolü yapın
3. Browser console'da notification loglarını kontrol edin

### ConfigService Bulunamıyor Hatası

**Çözüm:**
1. `quasar.config.ts`'de boot sırasını kontrol edin: `['config', 'logger', 'notify-defaults', ...]`
2. `src/boot/notify-defaults.ts` dosyasının mevcut olduğunu kontrol edin
3. `useSafeNotify()` composable'ını kullanın

### Notification Çok Sık Gösteriliyor

**Çözüm:**
1. `VITE_NOTIFICATION_DEFAULT_GROUP="true"` ayarını kullanın
2. Aynı mesajları gruplandırmak için timeout ayarlayın
3. Debounce mekanizması ekleyin
