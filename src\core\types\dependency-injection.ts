/**
 * Dependency Injection için TypeScript tip tanımları
 *
 * Bu dosya, DI sisteminde kullanılan genel tipleri içerir.
 */

import type { InjectionKey } from 'vue';

/**
 * Servis yaşam döngüsü tipleri
 */
export enum ServiceLifetime {
  /** Uygulama boyunca tek instance (Singleton) */
  SINGLETON = 'singleton',
  /** Her inject'te yeni instance */
  TRANSIENT = 'transient',
  /** Scope bazında tek instance (örn: request scope) */
  SCOPED = 'scoped',
}

/**
 * Servis metadata'sı
 */
export interface ServiceMetadata {
  /** Servis adı */
  name: string;
  /** Servis açıklaması */
  description?: string;
  /** Yaşam döngüsü */
  lifetime: ServiceLifetime;
  /** Bağımlılıklar */
  dependencies?: string[];
  /** Servisin kritik olup olmadığı */
  critical?: boolean;
  /** Servis versiyonu */
  version?: string;
}

/**
 * Servis kayıt bilgisi
 */
export interface ServiceRegistration<T = unknown> {
  /** InjectionKey */
  key: InjectionKey<T>;
  /** Servis metadata'sı */
  metadata: ServiceMetadata;
  /** Factory fonksiyonu */
  factory?: () => T;
  /** Singleton instance (eğer varsa) */
  instance?: T;
}

/**
 * Servis container arayüzü
 */
export interface IServiceContainer {
  /** Servis kaydet */
  register<T>(registration: ServiceRegistration<T>): void;

  /** Servis çözümle */
  resolve<T>(key: InjectionKey<T>): T;

  /** Servis mevcut mu kontrol et */
  isRegistered<T>(key: InjectionKey<T>): boolean;

  /** Tüm kayıtlı servisleri getir */
  getRegistrations(): ServiceRegistration[];

  /** Container'ı temizle */
  clear(): void;
}

/**
 * Servis factory fonksiyonu tipi
 */
export type ServiceFactory<T> = (container: IServiceContainer) => T;

/**
 * Servis provider arayüzü
 */
export interface IServiceProvider {
  /** Servisleri kaydet */
  configureServices(container: IServiceContainer): void;
}

/**
 * Dependency injection konfigürasyonu
 */
export interface DIConfiguration {
  /** Otomatik servis keşfi */
  autoDiscovery?: boolean;
  /** Circular dependency kontrolü */
  circularDependencyCheck?: boolean;
  /** Debug modu */
  debug?: boolean;
  /** Lazy loading */
  lazyLoading?: boolean;
}

/**
 * Servis bağımlılık ağacı
 */
export interface ServiceDependencyTree {
  /** Servis adı */
  service: string;
  /** Bağımlılıklar */
  dependencies: ServiceDependencyTree[];
  /** Circular dependency var mı */
  hasCircularDependency?: boolean;
}

/**
 * Servis health check sonucu
 */
export interface ServiceHealthResult {
  /** Servis adı */
  service: string;
  /** Sağlıklı mı */
  healthy: boolean;
  /** Hata mesajı (varsa) */
  error?: string;
  /** Response süresi (ms) */
  responseTime?: number;
  /** Ek bilgiler */
  metadata?: Record<string, unknown>;
}

/**
 * Servis health check arayüzü
 */
export interface IServiceHealthCheck {
  /** Health check yap */
  checkHealth(): Promise<ServiceHealthResult>;

  /** Servis adı */
  serviceName: string;
}

/**
 * Servis interceptor arayüzü
 */
export interface IServiceInterceptor<T = unknown> {
  /** Servis oluşturulmadan önce */
  beforeCreate?(metadata: ServiceMetadata): void;

  /** Servis oluşturulduktan sonra */
  afterCreate?(instance: T, metadata: ServiceMetadata): T;

  /** Servis çözümlenmeden önce */
  beforeResolve?(key: InjectionKey<T>): void;

  /** Servis çözümlendikten sonra */
  afterResolve?(instance: T, key: InjectionKey<T>): T;
}

/**
 * Servis decorator metadata'sı
 */
export interface ServiceDecoratorMetadata {
  /** Servis adı */
  name?: string;
  /** Yaşam döngüsü */
  lifetime?: ServiceLifetime;
  /** Bağımlılıklar */
  dependencies?: InjectionKey<unknown>[];
  /** Kritik servis mi */
  critical?: boolean;
}

/**
 * Async servis factory tipi
 */
export type AsyncServiceFactory<T> = (container: IServiceContainer) => Promise<T>;

/**
 * Conditional servis factory tipi
 */
export type ConditionalServiceFactory<T> = (container: IServiceContainer) => T | undefined;

/**
 * Servis proxy arayüzü
 * Lazy loading ve interceptor'lar için
 */
export interface IServiceProxy<T> {
  /** Gerçek servisi getir */
  getTarget(): T;

  /** Proxy aktif mi */
  isProxyActive(): boolean;

  /** Proxy'yi devre dışı bırak */
  disableProxy(): void;
}

/**
 * Servis scope arayüzü
 */
export interface IServiceScope extends Disposable {
  /** Scope'ta servis çözümle */
  resolve<T>(key: InjectionKey<T>): T;

  /** Scope'ta servis kaydet */
  register<T>(registration: ServiceRegistration<T>): void;

  /** Scope ID */
  readonly id: string;

  /** Parent scope */
  readonly parent?: IServiceScope;
}

/**
 * Disposable arayüzü
 */
export interface Disposable {
  /** Kaynakları temizle */
  dispose(): void | Promise<void>;
}

/**
 * Servis event'leri
 */
export interface ServiceEvents {
  /** Servis kaydedildi */
  'service:registered': { registration: ServiceRegistration };

  /** Servis çözümlendi */
  'service:resolved': { key: InjectionKey<unknown>; instance: unknown };

  /** Servis hatası */
  'service:error': { key: InjectionKey<unknown>; error: Error };

  /** Container temizlendi */
  'container:cleared': Record<string, never>;
}

/**
 * Servis event emitter arayüzü
 */
export interface IServiceEventEmitter {
  /** Event dinle */
  on<K extends keyof ServiceEvents>(event: K, listener: (data: ServiceEvents[K]) => void): void;

  /** Event emit et */
  emit<K extends keyof ServiceEvents>(event: K, data: ServiceEvents[K]): void;

  /** Event listener'ı kaldır */
  off<K extends keyof ServiceEvents>(event: K, listener: (data: ServiceEvents[K]) => void): void;
}

/**
 * Utility tipler
 */

/** Servis constructor tipi */
export type ServiceConstructor<T = unknown> = new (...args: unknown[]) => T;

/** Servis instance tipi */
export type ServiceInstance<T> = T extends ServiceConstructor<infer U> ? U : T;

/** InjectionKey'den tip çıkarma */
export type InferServiceType<T> = T extends InjectionKey<infer U> ? U : never;

/** Servis tuple'ından union tip oluşturma */
export type ServiceUnion<T extends readonly InjectionKey<unknown>[]> = {
  [K in keyof T]: InferServiceType<T[K]>;
}[number];

/** Servis map tipi */
export type ServiceMap<T extends Record<string, InjectionKey<unknown>>> = {
  [K in keyof T]: InferServiceType<T[K]>;
};

/**
 * Servis decorator'ları için metadata key'ler
 */
export const SERVICE_METADATA_KEY = Symbol('service:metadata');
export const INJECTABLE_METADATA_KEY = Symbol('injectable:metadata');
export const DEPENDENCY_METADATA_KEY = Symbol('dependency:metadata');
