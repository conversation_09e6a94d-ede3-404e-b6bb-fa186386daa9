/**
 * Firebase Boot File
 * 
 * Bu boot dosyası Firebase'i başlatır ve FirebaseDataService'i
 * uygulamaya provide eder. ConfigService'den Firebase ayarlarını
 * alır ve data source type'ına göre koşullu başlatma yapar.
 * 
 * Boot sırası:
 * 1. config.ts (ConfigService)
 * 2. logger.ts (LoggerService)
 * 3. notify-defaults.ts (Notify Defaults)
 * 4. firebase.ts (Firebase) <- Bu dosya
 * 5. Diğer servisler...
 * 
 * SOLID Prensipleri:
 * - Dependency Inversion: IConfigService ve ILogger abstraction'larına bağımlı
 * - Single Responsibility: Sadece Firebase başlatmaktan sorumlu
 */

import { boot } from 'quasar/wrappers';

// Servis ve interface import'ları
import { FirebaseDataService } from 'src/core/data-layer/implementations/firebase/FirebaseDataService';
import type { IDataService } from 'src/core/data-layer/IDataService';
import type { IConfigService } from 'src/core/config/IConfigService';
import type { ILogger } from 'src/core/services/logger/ILogger';

// Injection key'leri
import { configServiceKey, loggerKey, dataServiceKey } from 'src/constants/keys';

/**
 * Firebase Boot Function
 * 
 * Bu fonksiyon:
 * 1. ConfigService'i app context'inden alır
 * 2. Logger'ı app context'inden alır
 * 3. Data source type'ını kontrol eder
 * 4. Firebase ise FirebaseDataService'i başlatır
 * 5. DataService'i global olarak provide eder
 */
export default boot(({ app }) => {
  try {
    // ConfigService'i app context'inden al
    const configService = app._context.provides[configServiceKey as symbol] as IConfigService;
    
    if (!configService) {
      console.error('❌ [FIREBASE BOOT] ConfigService not found! Make sure config.ts boot file runs before firebase.ts');
      throw new Error('ConfigService is required for Firebase initialization');
    }

    // Logger'ı app context'inden al (opsiyonel)
    const logger = app._context.provides[loggerKey as symbol] as ILogger;

    // Data source type'ını kontrol et
    const dataSourceType = configService.getDataSourceType();
    
    if (dataSourceType !== 'firebase') {
      // Firebase değilse bu boot dosyasını atla
      if (logger) {
        logger.info('Skipping Firebase initialization', {
          dataSourceType,
          bootFile: 'firebase.ts',
        });
      } else {
        console.info('🔥 [FIREBASE BOOT] Skipping Firebase initialization, data source type:', dataSourceType);
      }
      return;
    }

    // Firebase data service'i oluştur
    const firebaseDataService: IDataService = new FirebaseDataService(configService, logger);
    
    // Firebase'i başlat (async olarak)
    firebaseDataService.connect().then(() => {
      if (logger) {
        logger.info('Firebase data service initialized successfully', {
          dataSourceType,
          bootFile: 'firebase.ts',
          environment: configService.isDevelopment() ? 'development' : 'production',
        });
      } else {
        console.info('🔥 [FIREBASE BOOT] Firebase data service initialized successfully');
      }
    }).catch((error) => {
      if (logger) {
        logger.error('Firebase data service initialization failed', {
          dataSourceType,
          bootFile: 'firebase.ts',
        }, error);
      } else {
        console.error('❌ [FIREBASE BOOT] Firebase data service initialization failed:', error);
      }
    });

    // DataService'i global olarak provide et
    app.provide(dataServiceKey, firebaseDataService);
    
    // Başarılı başlatma logunu yazdır
    if (logger) {
      logger.info('Firebase boot completed', {
        dataSourceType,
        bootFile: 'firebase.ts',
      });
    } else {
      console.info('🔥 [FIREBASE BOOT] Firebase boot completed');
    }

    // Development modunda ek bilgiler
    if (configService.isDevelopment()) {
      if (logger) {
        logger.debug('Firebase configuration', {
          projectId: configService.getFirebaseConfig()?.projectId,
          useEmulator: true,
        });
      }
    }

  } catch (error) {
    // Boot hatası - console'a yazdır
    console.error('❌ [FIREBASE BOOT] Failed to initialize Firebase:', error);
    
    // Hata durumunda fallback data service oluştur (mock)
    const fallbackDataService: IDataService = {
      // Temel CRUD
      create: async () => { throw new Error('Firebase not available'); },
      read: async () => { throw new Error('Firebase not available'); },
      update: async () => { throw new Error('Firebase not available'); },
      delete: async () => { throw new Error('Firebase not available'); },
      
      // Query
      list: async () => { throw new Error('Firebase not available'); },
      query: async () => { throw new Error('Firebase not available'); },
      findOne: async () => { throw new Error('Firebase not available'); },
      count: async () => { throw new Error('Firebase not available'); },
      
      // Batch
      batch: async () => { throw new Error('Firebase not available'); },
      createMany: async () => { throw new Error('Firebase not available'); },
      updateMany: async () => { throw new Error('Firebase not available'); },
      deleteMany: async () => { throw new Error('Firebase not available'); },
      
      // Subscriptions
      subscribe: () => { throw new Error('Firebase not available'); },
      subscribeToDocument: () => { throw new Error('Firebase not available'); },
      
      // Transaction
      runTransaction: async () => { throw new Error('Firebase not available'); },
      
      // Utility
      getDataSourceType: () => 'firebase',
      isConnected: async () => false,
      connect: async () => { throw new Error('Firebase not available'); },
      disconnect: async () => {},
      healthCheck: async () => ({
        status: 'unhealthy' as const,
        message: 'Firebase initialization failed',
        timestamp: new Date().toISOString(),
      }),
    };
    
    // Fallback data service'i provide et
    app.provide(dataServiceKey, fallbackDataService);
    
    console.warn('⚠️ [FIREBASE BOOT] Using fallback data service due to Firebase initialization failure');
    
    // Hata durumunda da uygulama çalışmaya devam etsin
    // throw error; // Bu satırı comment'ledik çünkü uygulama çökmemeli
  }
});

/**
 * Firebase Boot Dosyası Kullanım Notları:
 * 
 * 1. quasar.config.ts'e ekleme:
 *    boot: ['config', 'logger', 'notify-defaults', 'firebase', ...] // config, logger ve notify-defaults'dan sonra
 * 
 * 2. Ortam değişkenleri (.env.local):
 *    VITE_DATA_SOURCE_TYPE="firebase"
 *    VITE_FIREBASE_API_KEY="your-api-key"
 *    VITE_FIREBASE_AUTH_DOMAIN="your-project.firebaseapp.com"
 *    VITE_FIREBASE_PROJECT_ID="your-project-id"
 *    VITE_FIREBASE_STORAGE_BUCKET="your-project.appspot.com"
 *    VITE_FIREBASE_MESSAGING_SENDER_ID="123456789"
 *    VITE_FIREBASE_APP_ID="1:123456789:web:abcdef"
 * 
 * 3. Bileşenlerde kullanım:
 *    import { inject } from 'vue';
 *    import { dataServiceKey } from 'src/constants/keys';
 *    
 *    const dataService = inject(dataServiceKey)!;
 *    const users = await dataService.list('users');
 * 
 * 4. Composable'larda kullanım:
 *    export function useDataService() {
 *      const dataService = inject(dataServiceKey);
 *      if (!dataService) {
 *        throw new Error('DataService not provided');
 *      }
 *      return dataService;
 *    }
 * 
 * 5. Koşullu başlatma:
 *    - VITE_DATA_SOURCE_TYPE="firebase" ise Firebase başlatılır
 *    - Diğer durumlarda bu boot dosyası atlanır
 *    - REST API veya Mock için ayrı boot dosyaları kullanılır
 * 
 * 6. Hata durumları:
 *    - ConfigService bulunamadığında hata fırlatılır
 *    - Firebase başlatma hatası durumunda fallback service kullanılır
 *    - Uygulama çökmez, sadece data service kısıtlı olur
 *    - Development modunda detaylı hata bilgileri gösterilir
 */
