# Dependency Injection (DI) Stratejisi

Bu dokümanda, uygulamanın bağımlılık yönetimi stratejisi detaylandırılmıştır.

## Amaç
- Bileşenleri ve servisleri gevşek bağlı (loosely coupled) tutmak
- Test edilebilirliği artırmak
- SOLID prensiplerine uygun mimari oluşturmak
- Bağımlılıkların tersine çevrilmesi prensibini (DIP) uygulamak

## DI Mekanizmaları

### 1. Vue `provide`/`inject` Kullanımı

**Senaryolar:**
- Uygulama genelinde paylaşılması gereken servis örnekleri
- Global konfigürasyonlar
- Çekirdek altyapı servisleri (ConfigService, Logger, DataService)

**Sağlama Yeri:**
- Quasar boot dosyalarında `app.provide(key, instance)` ile
- Uygulama başlangıcında bir kez oluşturulur

**Enjekte Etme Yeri:**
- Bileşenlerin `setup()` fonksiyonunda
- Composable'lar içinde
- Diğer servislerin constructor'larında

**Örnek:**
```typescript
// Boot dosyasında
app.provide(configServiceKey, new ConfigService());

// Bileşende
const configService = inject(configServiceKey)!;

// Composable'da
export function useConfig() {
  const configService = inject(configServiceKey)!;
  return configService;
}
```

### 2. Pinia Store'larının Kullanımı

**Senaryolar:**
- State yönetimi
- Reactive veri
- Action'lar ve getter'lar
- Bileşenler arası veri paylaşımı

**Kullanım:**
```typescript
// Store tanımı
export const useAuthStore = defineStore('auth', () => {
  // State, actions, getters
});

// Bileşende
const authStore = useAuthStore();
```

### 3. Constructor Injection (Servisler İçin)

**Senaryolar:**
- Bir servisin başka servislere bağımlılığı
- Servis katmanında bağımlılık yönetimi

**Yaklaşım:**
```typescript
export class LoggerService implements ILogger {
  constructor(private configService: IConfigService) {}
}

// Boot dosyasında
const configService = inject(configServiceKey)!;
const loggerService = new LoggerService(configService);
```

### 4. Doğrudan Import (Dikkatli Kullanım)

**Senaryolar:**
- State içermeyen utility fonksiyonları
- Basit singleton servisler
- Pure fonksiyonlar

**Dikkat:**
- Test edilebilirliği zorlaştırabilir
- Sadece stateless utility'ler için kullanılmalı

## InjectionKey Kullanımı

### Tip Güvenliği
- Her servis için `InjectionKey<T>` tanımlanır
- `src/constants/keys.ts` dosyasında merkezi yönetim
- Interface'lere bağlı tip güvenliği

### Örnek Kullanım
```typescript
// keys.ts
export const configServiceKey: InjectionKey<IConfigService> = Symbol('ConfigService');

// Boot dosyasında
app.provide(configServiceKey, configServiceInstance);

// Kullanım yerinde
const configService = inject(configServiceKey)!;
```

## Test Edilebilirlik

### Mock Stratejisi
```typescript
// Test dosyasında
const mockConfigService: IConfigService = {
  getAppName: () => 'Test App',
  // ... diğer metodlar
};

// Test setup'ında
global: {
  provide: {
    [configServiceKey as symbol]: mockConfigService
  }
}
```

### Test Prensipleri
- Her servis interface'e bağlı olduğu için mock edilebilir
- Dependency injection sayesinde test izolasyonu sağlanır
- Unit testlerde gerçek bağımlılıklar yerine mock'lar kullanılır

## DI Prensipleri

### 1. Dependency Inversion Principle (DIP)
- Yüksek seviyeli modüller düşük seviyeli modüllere bağımlı olmamalı
- Her ikisi de soyutlamalara (interface'ler) bağımlı olmalı
- Concrete implementasyonlar değil, interface'ler kullanılır

### 2. Loose Coupling (Gevşek Bağlılık)
- Bileşenler birbirlerini doğrudan bilmez
- Interface'ler aracılığıyla iletişim kurarlar
- Implementasyon değişiklikleri diğer bileşenleri etkilemez

### 3. Single Responsibility
- Her servis tek sorumluluğa sahip
- Bağımlılıklar açık ve net tanımlanır
- Interface'ler odaklanmış ve minimal

## Boot Dosyaları Sıralaması

DI için boot dosyalarının doğru sırada yüklenmesi kritiktir:

1. `config.ts` - ConfigService (temel bağımlılık)
2. `logger.ts` - LoggerService (ConfigService'e bağımlı)
3. `notify-defaults.ts` - Notify ayarları (ConfigService'e bağımlı)
4. `axios.ts` - HTTP client (ConfigService'e bağımlı)
5. `firebase.ts` - Firebase init (ConfigService'e bağımlı)
6. `data-service.ts` - DataService factory (tüm yukarıdakilere bağımlı)
7. `i18n.ts` - i18n (ConfigService'e bağımlı)
8. `theme.ts` - Theme (ConfigService'e bağımlı)

## En İyi Pratikler

### Do's ✅
- Interface'leri kullan
- InjectionKey'leri tip güvenliği için kullan
- Constructor injection'ı tercih et
- Bağımlılıkları açık şekilde tanımla
- Test edilebilirliği göz önünde bulundur

### Don'ts ❌
- Concrete sınıflara doğrudan bağımlılık oluşturma
- Global state'i servisler içinde kullanma
- Circular dependency'ler oluşturma
- any tipini kullanma
- Service locator pattern'i kullanma

Bu strateji sayesinde maintainable, testable ve SOLID prensiplerine uygun bir DI sistemi kurulur.
