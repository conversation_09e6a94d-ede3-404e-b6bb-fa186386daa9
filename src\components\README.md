# Components Dizini

Bu dizin **genel/paylaşılan mikro UI bileşenlerini** içerir.

## Amaç

- Uygulama genelinde kullanılabilecek yeniden kullanılabilir bileşenler
- Modül-bağımsız UI bileşenleri
- Temel UI building block'ları

## Örnekler

- `BaseButton.vue` - Temel buton bileşeni
- `BaseInput.vue` - Temel input bileşeni
- `LoadingSpinner.vue` - Yükleme göstergesi
- `ConfirmDialog.vue` - Onay dialog'u
- `LanguageSwitcher.vue` - <PERSON><PERSON> değiştirici
- `ThemeSwitcher.vue` - Tema değiştirici

## Kurallar

- Her bileşen kendi sorumluluğuna odaklanmalı (SRP)
- Props ve emits açıkça tanımlanmalı
- TypeScript ile tip güvenliği sağlanmalı
- Slot'lar uygun şekilde kullanılmalı
- Quasar bileşenlerini base alabilir

## Modül-Spesifik Bileşenler

Sadece belirli bir modülde kullanılan bileşenler `src/modules/<module-name>/components/` dizininde olmalıdır.
