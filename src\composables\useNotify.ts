/**
 * Notification Composable
 *
 * <PERSON>u composable, Quasar Notify plugin'ini sa<PERSON>
 * ConfigService ile entegre ç<PERSON>, tip güvenli ve
 * kolay kullanımlı notification fonksiyonları sağlar.
 *
 * Özellikler:
 * - ConfigService entegrasyonu (display level kontrolü)
 * - Logger entegrasyonu (notification logları)
 * - Tip güvenli notification metodları
 * - API error handling
 * - Friendly error messages
 * - Performance optimized
 */

import { inject } from 'vue';
import { Notify, type QNotifyCreateOptions } from 'quasar';

// Servis ve interface import'ları
import type {
  IConfigService,
  NotificationLevel,
  ApiErrorNotificationType,
} from 'src/core/config/IConfigService';
import type { ILogger } from 'src/core/services/logger/ILogger';

// Injection key'leri
import { configServiceKey, loggerKey } from 'src/constants/keys';

/**
 * Notification seviyeleri ve Quasar tipleri arasındaki mapping
 */
const NOTIFICATION_TYPE_MAP: Record<NotificationLevel, string> = {
  error: 'negative',
  warn: 'warning',
  info: 'info',
  success: 'positive',
  none: 'info', // Fallback
} as const;

/**
 * Notification seviye öncelikleri
 * Düşük sayı = yüksek öncelik
 * Success notification'ları info seviyesinde gösterilmeli
 */
const NOTIFICATION_LEVEL_PRIORITY: Record<NotificationLevel, number> = {
  error: 0,
  warn: 1,
  success: 2, // Success'i info ile aynı seviyeye getirdik
  info: 2,
  none: 4,
} as const;

/**
 * Notification options interface
 */
export interface NotificationOptions extends Omit<QNotifyCreateOptions, 'type'> {
  /** Notification seviyesi */
  level?: NotificationLevel;
  /** Logger'a da yazdırılsın mı */
  logToConsole?: boolean;
  /** Context bilgileri (logger için) */
  context?: Record<string, unknown>;
}

/**
 * API Error notification options
 */
export interface ApiErrorOptions {
  /** Hata objesi */
  error: Error;
  /** Kullanıcı dostu mesaj */
  friendlyMessage?: string;
  /** Ek context bilgileri */
  context?: Record<string, unknown>;
  /** Notification type override */
  notificationType?: ApiErrorNotificationType;
}

/**
 * Notification Composable
 *
 * Quasar Notify'yi ConfigService ile entegre eden composable.
 *
 * @returns Notification functions
 *
 * @example
 * ```typescript
 * // Bileşende kullanım
 * import { useNotify } from 'src/composables/useNotify';
 *
 * export default defineComponent({
 *   setup() {
 *     const notify = useNotify();
 *
 *     const handleSuccess = () => {
 *       notify.success('Operation completed successfully!');
 *     };
 *
 *     const handleError = (error: Error) => {
 *       notify.apiError({
 *         error,
 *         friendlyMessage: 'Something went wrong. Please try again.'
 *       });
 *     };
 *
 *     return {
 *       handleSuccess,
 *       handleError
 *     };
 *   }
 * });
 * ```
 */
export function useNotify() {
  // ConfigService'i inject et
  const configService = inject<IConfigService>(configServiceKey);
  const logger = inject<ILogger>(loggerKey);

  if (!configService) {
    console.warn('⚠️ [useNotify] ConfigService not found, using default notification behavior');
  }

  /**
   * Notification seviyesinin gösterilip gösterilmeyeceğini kontrol eder
   */
  const isLevelEnabled = (level: NotificationLevel): boolean => {
    if (!configService) return true;

    const displayLevel = configService.getNotificationDisplayLevel();
    if (displayLevel === 'none') return false;

    return NOTIFICATION_LEVEL_PRIORITY[level] <= NOTIFICATION_LEVEL_PRIORITY[displayLevel];
  };

  /**
   * Temel notification fonksiyonu
   */
  const notify = (message: string, options: NotificationOptions = {}) => {
    const { level = 'info', logToConsole = true, context, ...quasarOptions } = options;

    // Seviye kontrolü
    if (!isLevelEnabled(level)) {
      return;
    }

    // Logger'a yazdır
    if (logToConsole && logger) {
      // NotificationLevel'dan LogLevel'a mapping
      const logLevel = level === 'success' ? 'info' : level === 'none' ? 'debug' : level;
      logger.logWithLevel(logLevel, `Notification: ${message}`, {
        notificationLevel: level,
        ...context,
      });
    }

    // Quasar Notify'yi çağır
    const notifyOptions: QNotifyCreateOptions = {
      message,
      type: NOTIFICATION_TYPE_MAP[level],
      ...quasarOptions,
    };

    return Notify.create(notifyOptions);
  };

  /**
   * Success notification
   */
  const success = (message: string, options: Omit<NotificationOptions, 'level'> = {}) => {
    return notify(message, { ...options, level: 'success' });
  };

  /**
   * Info notification
   */
  const info = (message: string, options: Omit<NotificationOptions, 'level'> = {}) => {
    return notify(message, { ...options, level: 'info' });
  };

  /**
   * Warning notification
   */
  const warning = (message: string, options: Omit<NotificationOptions, 'level'> = {}) => {
    return notify(message, { ...options, level: 'warn' });
  };

  /**
   * Error notification
   */
  const error = (message: string, options: Omit<NotificationOptions, 'level'> = {}) => {
    return notify(message, { ...options, level: 'error' });
  };

  /**
   * API Error notification
   * ConfigService'den alınan ayarlara göre davranır
   */
  const apiError = (options: ApiErrorOptions) => {
    const {
      error: errorObj,
      friendlyMessage = 'An error occurred. Please try again.',
      context = {},
      notificationType,
    } = options;

    // ConfigService'den API error notification type'ını al
    const configNotificationType = configService?.getApiErrorNotificationType() || 'friendly';
    const finalNotificationType = notificationType || configNotificationType;

    // Logger'a her zaman yazdır
    if (logger) {
      logger.error(
        'API Error occurred',
        {
          errorMessage: errorObj.message,
          friendlyMessage,
          notificationType: finalNotificationType,
          ...context,
        },
        errorObj,
      );
    }

    // Notification type'ına göre davran
    switch (finalNotificationType) {
      case 'toast':
        // Teknik hata mesajını göster
        return error(errorObj.message, {
          logToConsole: false, // Zaten logger'a yazdık
          context,
          timeout: 8000, // Uzun timeout
          multiLine: true,
        });

      case 'friendly':
        // Kullanıcı dostu mesajı göster
        return error(friendlyMessage, {
          logToConsole: false, // Zaten logger'a yazdık
          context,
          timeout: 6000,
        });

      case 'silent':
        // Notification gösterme, sadece logla
        return;

      default:
        // Fallback: friendly
        return error(friendlyMessage, {
          logToConsole: false,
          context,
        });
    }
  };

  /**
   * Loading notification
   * Spinner ile birlikte gösterir
   */
  const loading = (
    message: string = 'Loading...',
    options: Omit<NotificationOptions, 'level'> = {},
  ) => {
    return notify(message, {
      ...options,
      level: 'info',
      spinner: true,
      timeout: 0, // Manuel olarak kapatılana kadar göster
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {
            // Notification'ı kapat
          },
        },
      ],
    });
  };

  /**
   * Progress notification
   * Progress bar ile birlikte gösterir
   */
  const progress = (
    message: string,
    progressValue: number,
    options: Omit<NotificationOptions, 'level'> = {},
  ) => {
    return notify(message, {
      ...options,
      level: 'info',
      timeout: 0,
      progress: true,
      html: true,
      message: `
        <div>${message}</div>
        <div class="q-mt-sm">
          <q-linear-progress :value="${progressValue / 100}" color="primary" />
          <div class="text-center q-mt-xs">${Math.round(progressValue)}%</div>
        </div>
      `,
    });
  };

  return {
    // Temel fonksiyonlar
    notify,
    success,
    info,
    warning,
    error,

    // Özel fonksiyonlar
    apiError,
    loading,
    progress,

    // Utility fonksiyonlar
    isLevelEnabled,
  };
}

/**
 * Safe Notify Composable
 *
 * ConfigService bulunamadığında hata fırlatmak yerine
 * basit notification davranışı gösterir.
 */
export function useSafeNotify() {
  try {
    return useNotify();
  } catch (error) {
    console.warn('⚠️ [useSafeNotify] Failed to initialize notify, using fallback', error);

    // Fallback implementation
    return {
      notify: (message: string) => Notify.create({ message }),
      success: (message: string) => Notify.create({ message, type: 'positive' }),
      info: (message: string) => Notify.create({ message, type: 'info' }),
      warning: (message: string) => Notify.create({ message, type: 'warning' }),
      error: (message: string) => Notify.create({ message, type: 'negative' }),
      apiError: ({ friendlyMessage }: ApiErrorOptions) =>
        Notify.create({ message: friendlyMessage || 'An error occurred', type: 'negative' }),
      loading: (message: string = 'Loading...') =>
        Notify.create({ message, spinner: true, timeout: 0 }),
      progress: (message: string, progressValue: number) =>
        Notify.create({ message: `${message} (${Math.round(progressValue)}%)` }),
      isLevelEnabled: () => true,
    };
  }
}

/**
 * Notification Composable Kullanım Örnekleri:
 *
 * 1. Temel Kullanım:
 *    const notify = useNotify();
 *    notify.success('Operation completed!');
 *
 * 2. API Error Handling:
 *    const notify = useNotify();
 *    try {
 *      await apiCall();
 *    } catch (error) {
 *      notify.apiError({
 *        error,
 *        friendlyMessage: 'Failed to save data. Please try again.'
 *      });
 *    }
 *
 * 3. Loading Notification:
 *    const notify = useNotify();
 *    const loadingNotification = notify.loading('Saving data...');
 *    try {
 *      await saveData();
 *      loadingNotification(); // Kapat
 *      notify.success('Data saved successfully!');
 *    } catch (error) {
 *      loadingNotification(); // Kapat
 *      notify.apiError({ error });
 *    }
 *
 * 4. Progress Notification:
 *    const notify = useNotify();
 *    for (let i = 0; i <= 100; i += 10) {
 *      notify.progress('Uploading file...', i);
 *      await delay(100);
 *    }
 *
 * 5. Custom Options:
 *    const notify = useNotify();
 *    notify.info('Custom notification', {
 *      timeout: 10000,
 *      position: 'bottom-left',
 *      actions: [
 *        {
 *          label: 'Undo',
 *          color: 'yellow',
 *          handler: () => undoAction()
 *        }
 *      ]
 *    });
 *
 * 6. Güvenli Kullanım:
 *    const notify = useSafeNotify(); // Hata fırlatmaz
 *    notify.info('This always works');
 */
