/**
 * Configuration Service Interface
 * 
 * Bu interface, uygulamanın ihtiyaç duyduğu tüm yapılandırma değerlerini
 * (işlenmiş ve tip güvenli) getiren metodları tanımlar.
 * 
 * Sorumluluklar:
 * - Ortam değişkenlerini tip güvenli şekilde sunma
 * - Varsayılan değerleri yönetme
 * - Konfigürasyon validasyonu
 * - Environment-specific ayarlar
 */

/**
 * Veri kaynağı tipleri
 */
export type DataSourceType = 'firebase' | 'restapi' | 'mock';

/**
 * Log seviyeleri
 */
export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';

/**
 * Tema tipleri
 */
export type ThemeType = 'light' | 'dark' | 'auto';

/**
 * Notifikasyon seviyeleri
 */
export type NotificationLevel = 'error' | 'warn' | 'info' | 'success' | 'none';

/**
 * API hata notifikasyon tipleri
 */
export type ApiErrorNotificationType = 'toast' | 'friendly' | 'silent';

/**
 * Quasar Notify pozisyonları
 * Quasar'ın desteklediği tüm pozisyonlar
 */
export type QNotifyPosition = 
  | 'top-left' 
  | 'top' 
  | 'top-right' 
  | 'left' 
  | 'center' 
  | 'right' 
  | 'bottom-left' 
  | 'bottom' 
  | 'bottom-right';

/**
 * Firebase konfigürasyon tipi
 */
export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId?: string;
}

/**
 * Configuration Service Interface
 * 
 * Tüm konfigürasyon değerlerini tip güvenli şekilde sunar.
 * Implementasyon, ortam değişkenlerini okur ve işler.
 */
export interface IConfigService {
  // ===========================================
  // UYGULAMA GENEL AYARLARI
  // ===========================================
  
  /**
   * Uygulama adını döndürür
   * @returns Uygulama adı
   */
  getAppName(): string;
  
  /**
   * Uygulama versiyonunu döndürür
   * @returns Uygulama versiyonu
   */
  getAppVersion(): string;

  // ===========================================
  // VERİ KAYNAĞI AYARLARI
  // ===========================================
  
  /**
   * Veri kaynağı tipini döndürür
   * @returns Veri kaynağı tipi
   */
  getDataSourceType(): DataSourceType;

  // ===========================================
  // REST API AYARLARI
  // ===========================================
  
  /**
   * API base URL'ini döndürür
   * @returns API base URL (sadece REST API kullanılıyorsa)
   */
  getApiBaseUrl(): string | undefined;
  
  /**
   * API timeout değerini döndürür (milisaniye)
   * @returns API timeout değeri
   */
  getApiTimeout(): number | undefined;

  // ===========================================
  // FIREBASE AYARLARI
  // ===========================================
  
  /**
   * Firebase konfigürasyonunu döndürür
   * @returns Firebase config (sadece Firebase kullanılıyorsa)
   */
  getFirebaseConfig(): FirebaseConfig | undefined;

  // ===========================================
  // ÇOKLU DİL (i18n) AYARLARI
  // ===========================================
  
  /**
   * Varsayılan locale'i döndürür
   * @returns Varsayılan locale (örn: 'tr-TR')
   */
  getDefaultLocale(): string;
  
  /**
   * Fallback locale'i döndürür
   * @returns Fallback locale (örn: 'en-US')
   */
  getFallbackLocale(): string;

  // ===========================================
  // TEMA AYARLARI
  // ===========================================
  
  /**
   * Varsayılan temayı döndürür
   * @returns Varsayılan tema
   */
  getDefaultTheme(): ThemeType;

  // ===========================================
  // LOGLAMA AYARLARI
  // ===========================================
  
  /**
   * Log seviyesini döndürür
   * @returns Log seviyesi
   */
  getLogLevel(): LogLevel;

  // ===========================================
  // NOTİFİKASYON AYARLARI
  // ===========================================
  
  /**
   * Notifikasyon görüntüleme seviyesini döndürür
   * @returns Notifikasyon seviyesi
   */
  getNotificationDisplayLevel(): NotificationLevel;
  
  /**
   * API hata notifikasyon tipini döndürür
   * @returns API hata notifikasyon tipi
   */
  getApiErrorNotificationType(): ApiErrorNotificationType;
  
  /**
   * Notifikasyon varsayılan pozisyonunu döndürür
   * @returns Quasar Notify pozisyonu
   */
  getNotificationDefaultPosition(): QNotifyPosition;
  
  /**
   * Notifikasyon varsayılan timeout değerini döndürür (milisaniye)
   * @returns Timeout değeri
   */
  getNotificationDefaultTimeout(): number;
  
  /**
   * Notifikasyon gruplandırma ayarını döndürür
   * @returns Gruplandırma aktif mi
   */
  getNotificationDefaultGroup(): boolean;
  
  /**
   * Notifikasyon kapatma butonu etiketini döndürür
   * @returns Kapatma butonu etiketi (string) veya varsayılan (boolean)
   */
  getNotificationDefaultCloseBtnLabel(): string | boolean;

  // ===========================================
  // ENVIRONMENT KONTROL METODLARI
  // ===========================================
  
  /**
   * Development mode'da mı kontrol eder
   * @returns Development mode aktif mi
   */
  isDevelopment(): boolean;
  
  /**
   * Production mode'da mı kontrol eder
   * @returns Production mode aktif mi
   */
  isProduction(): boolean;

  // ===========================================
  // VALIDATION VE UTILITY METODLARI
  // ===========================================
  
  /**
   * Konfigürasyonun geçerli olup olmadığını kontrol eder
   * @returns Konfigürasyon geçerli mi
   */
  isValid(): boolean;
  
  /**
   * Konfigürasyon hatalarını döndürür
   * @returns Hata listesi (varsa)
   */
  getValidationErrors(): string[];
  
  /**
   * Konfigürasyon özetini döndürür (debug için)
   * @returns Konfigürasyon özeti
   */
  getConfigSummary(): Record<string, unknown>;
}
