# Utils Dizini

Bu dizin **genel yardımcı fonksiyonları** içerir.

## Amaç

- Uygulama genelinde kullanılan utility fonksiyonları
- State içermeyen pure fonksiyonlar
- Veri manipülasyonu ve formatlaması
- Validation fonksiyonları

## Örnekler

- `date.ts` - Tarih formatlaması ve manipülasyonu
- `string.ts` - String işlemleri
- `validation.ts` - Validation fonksiyonları
- `format.ts` - Veri formatlaması
- `storage.ts` - Local/Session storage yardımcıları
- `api.ts` - API yardımcı fonksiyonları

## Dosya Örnekleri

### date.ts

```typescript
export const formatDate = (date: Date, format: string): string => {
  // Tarih formatlaması
};

export const isValidDate = (date: string): boolean => {
  // Tarih validasyonu
};
```

### validation.ts

```typescript
export const isValidEmail = (email: string): boolean => {
  // Email validasyonu
};

export const isValidPhone = (phone: string): boolean => {
  // Telefon validasyonu
};
```

## Kurallar

- Pure fonksiyonlar olmalı (side effect yok)
- TypeScript ile tip güvenliği sağlanmalı
- Unit test edilebilir olmalı
- Tek sorumluluk prensibi (SRP)
- İyi dokümante edilmeli (JSDoc)

## Composables vs Utils

- **Utils**: State içermeyen pure fonksiyonlar
- **Composables**: Reactive state içeren Vue-spesifik logic
