/**
 * Firebase Data Service Implementation
 *
 * IDataService interface'ini Firebase Firestore ile implement eden sınıf.
 * ConfigService'den Firebase ayarlarını alır ve Firestore operasyonlarını
 * IDataService API'si üzerinden sunar.
 *
 * Ö<PERSON><PERSON>ler:
 * - Firebase Firestore entegrasyonu
 * - Real-time subscriptions
 * - Transaction desteği
 * - Batch operations
 * - Query ve filtreleme
 * - Error handling
 */

import { initializeApp, type FirebaseApp, type FirebaseOptions } from 'firebase/app';
import {
  getFirestore,
  type Firestore,
  collection,
  doc,
  addDoc,
  getDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  endBefore,
  onSnapshot,
  writeBatch,
  runTransaction,
  connectFirestoreEmulator,
  type QueryConstraint,
  type DocumentData,
  type Unsubscribe,
  type Transaction,
} from 'firebase/firestore';

import type {
  IDataService,
  DataSourceType,
  QueryOptions,
  BatchOperation,
  SubscriptionCallback,
  UnsubscribeFunction,
  TransactionCallback,
  ITransaction,
} from '../../IDataService';
import { DataServiceError, DATA_SERVICE_ERROR_CODES } from '../../IDataService';
import type { IConfigService } from 'src/core/config/IConfigService';
import type { ILogger } from 'src/core/services/logger/ILogger';

/**
 * Firebase Transaction Wrapper
 * ITransaction interface'ini Firebase Transaction ile implement eder
 */
class FirebaseTransactionWrapper implements ITransaction {
  constructor(
    private transaction: Transaction,
    private firestore: Firestore,
    private logger?: ILogger,
  ) {}

  async get<T>(collection: string, id: string): Promise<T | null> {
    try {
      const docRef = doc(this.firestore, collection, id);
      const docSnap = await this.transaction.get(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      return { id: docSnap.id, ...docSnap.data() } as T;
    } catch (error) {
      this.logger?.error('Transaction get failed', { collection, id }, error as Error);
      throw new DataServiceError(
        `Failed to get document ${id} from ${collection}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }

  create<T>(collection: string, data: T): string {
    try {
      const docRef = doc(this.firestore, collection);
      this.transaction.set(docRef, data as DocumentData);
      return docRef.id;
    } catch (error) {
      this.logger?.error('Transaction create failed', { collection, data }, error as Error);
      throw new DataServiceError(
        `Failed to create document in ${collection}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  update<T>(collection: string, id: string, data: Partial<T>): void {
    try {
      const docRef = doc(this.firestore, collection, id);
      this.transaction.update(docRef, data as DocumentData);
    } catch (error) {
      this.logger?.error('Transaction update failed', { collection, id, data }, error as Error);
      throw new DataServiceError(
        `Failed to update document ${id} in ${collection}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }

  delete(collection: string, id: string): void {
    try {
      const docRef = doc(this.firestore, collection, id);
      this.transaction.delete(docRef);
    } catch (error) {
      this.logger?.error('Transaction delete failed', { collection, id }, error as Error);
      throw new DataServiceError(
        `Failed to delete document ${id} from ${collection}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }
}

/**
 * Firebase Data Service Implementation
 *
 * SOLID prensiplerine uygun olarak:
 * - Single Responsibility: Sadece Firebase Firestore işlemlerinden sorumlu
 * - Open/Closed: Yeni query türleri eklenebilir
 * - Liskov Substitution: IDataService'i tam olarak implement eder
 * - Interface Segregation: Sadece gerekli metodları içerir
 * - Dependency Inversion: IConfigService ve ILogger abstraction'larına bağımlı
 */
export class FirebaseDataService implements IDataService {
  private app: FirebaseApp | null = null;
  private firestore: Firestore | null = null;
  private isInitialized = false;

  constructor(
    private configService: IConfigService,
    private logger?: ILogger,
  ) {
    this.logger?.info('FirebaseDataService created', {
      dataSourceType: this.getDataSourceType(),
    });
  }

  // ===========================================
  // INITIALIZATION
  // ===========================================

  // eslint-disable-next-line @typescript-eslint/require-await
  async connect(): Promise<void> {
    if (this.isInitialized) {
      this.logger?.debug('Firebase already initialized');
      return;
    }

    try {
      // Firebase config'i ConfigService'den al
      const firebaseConfig = this.configService.getFirebaseConfig();

      if (!firebaseConfig) {
        throw new DataServiceError(
          'Firebase configuration not found',
          DATA_SERVICE_ERROR_CODES.CONNECTION_FAILED,
        );
      }

      this.logger?.info('Initializing Firebase', {
        projectId: firebaseConfig.projectId,
        environment: this.configService.isDevelopment() ? 'development' : 'production',
      });

      // Firebase app'i initialize et
      this.app = initializeApp(firebaseConfig as FirebaseOptions);
      this.firestore = getFirestore(this.app);

      // Development modunda emulator kullan
      if (this.configService.isDevelopment()) {
        try {
          connectFirestoreEmulator(this.firestore, 'localhost', 8080);
          this.logger?.info('Connected to Firestore emulator');
        } catch (error) {
          this.logger?.warn('Failed to connect to Firestore emulator, using production', {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      this.isInitialized = true;
      this.logger?.info('Firebase initialized successfully');
    } catch (error) {
      this.logger?.error('Firebase initialization failed', {}, error as Error);
      throw new DataServiceError(
        'Failed to initialize Firebase',
        DATA_SERVICE_ERROR_CODES.CONNECTION_FAILED,
        error,
      );
    }
  }

  async disconnect(): Promise<void> {
    if (this.app) {
      // Firebase'de explicit disconnect yok, sadece referansları temizle
      this.app = null;
      this.firestore = null;
      this.isInitialized = false;
      this.logger?.info('Firebase disconnected');
    }
    return Promise.resolve();
  }

  async isConnected(): Promise<boolean> {
    return Promise.resolve(this.isInitialized && this.firestore !== null);
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    message?: string;
    timestamp: string;
  }> {
    try {
      if (!this.firestore) {
        return {
          status: 'unhealthy',
          message: 'Firestore not initialized',
          timestamp: new Date().toISOString(),
        };
      }

      // Basit bir test query yap
      const testCollection = collection(this.firestore, '__health_check__');
      const testQuery = query(testCollection, limit(1));
      await getDocs(testQuery);

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger?.error('Firebase health check failed', {}, error as Error);
      return {
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }
  }

  getDataSourceType(): DataSourceType {
    return 'firebase';
  }

  // ===========================================
  // PRIVATE HELPERS
  // ===========================================

  private ensureInitialized(): Firestore {
    if (!this.firestore) {
      throw new DataServiceError(
        'Firebase not initialized. Call connect() first.',
        DATA_SERVICE_ERROR_CODES.CONNECTION_FAILED,
      );
    }
    return this.firestore;
  }

  private buildQuery(collectionName: string, options?: QueryOptions) {
    const firestore = this.ensureInitialized();
    const collectionRef = collection(firestore, collectionName);
    const constraints: QueryConstraint[] = [];

    if (options?.where) {
      for (const condition of options.where) {
        constraints.push(where(condition.field, condition.operator, condition.value));
      }
    }

    if (options?.orderBy) {
      for (const order of options.orderBy) {
        constraints.push(orderBy(order.field, order.direction));
      }
    }

    if (options?.limit) {
      constraints.push(limit(options.limit));
    }

    if (options?.startAfter) {
      constraints.push(startAfter(options.startAfter));
    }

    if (options?.endBefore) {
      constraints.push(endBefore(options.endBefore));
    }

    return query(collectionRef, ...constraints);
  }

  private documentToObject<T>(docSnap: {
    id: string;
    data: () => DocumentData;
    exists?: () => boolean;
  }): T {
    return {
      id: docSnap.id,
      ...docSnap.data(),
    } as T;
  }

  // ===========================================
  // TEMEL CRUD OPERASYONLARI
  // ===========================================

  async create<T>(collectionName: string, data: T): Promise<string> {
    try {
      const firestore = this.ensureInitialized();
      const collectionRef = collection(firestore, collectionName);
      const docRef = await addDoc(collectionRef, data as DocumentData);

      this.logger?.debug('Document created', { collection: collectionName, id: docRef.id });
      return docRef.id;
    } catch (error) {
      this.logger?.error('Create failed', { collection: collectionName, data }, error as Error);
      throw new DataServiceError(
        `Failed to create document in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  async read<T>(collectionName: string, id: string): Promise<T | null> {
    try {
      const firestore = this.ensureInitialized();
      const docRef = doc(firestore, collectionName, id);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        this.logger?.debug('Document not found', { collection: collectionName, id });
        return null;
      }

      const result = this.documentToObject<T>(docSnap);
      this.logger?.debug('Document read', { collection: collectionName, id });
      return result;
    } catch (error) {
      this.logger?.error('Read failed', { collection: collectionName, id }, error as Error);
      throw new DataServiceError(
        `Failed to read document ${id} from ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }

  async update<T>(collectionName: string, id: string, data: Partial<T>): Promise<void> {
    try {
      const firestore = this.ensureInitialized();
      const docRef = doc(firestore, collectionName, id);
      await updateDoc(docRef, data as DocumentData);

      this.logger?.debug('Document updated', { collection: collectionName, id });
    } catch (error) {
      this.logger?.error('Update failed', { collection: collectionName, id, data }, error as Error);
      throw new DataServiceError(
        `Failed to update document ${id} in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }

  async delete(collectionName: string, id: string): Promise<void> {
    try {
      const firestore = this.ensureInitialized();
      const docRef = doc(firestore, collectionName, id);
      await deleteDoc(docRef);

      this.logger?.debug('Document deleted', { collection: collectionName, id });
    } catch (error) {
      this.logger?.error('Delete failed', { collection: collectionName, id }, error as Error);
      throw new DataServiceError(
        `Failed to delete document ${id} from ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.DOCUMENT_NOT_FOUND,
        error,
      );
    }
  }

  // ===========================================
  // QUERY VE LİSTELEME
  // ===========================================

  async list<T>(collectionName: string, options?: QueryOptions): Promise<T[]> {
    try {
      const queryRef = this.buildQuery(collectionName, options);
      const querySnapshot = await getDocs(queryRef);

      const results = querySnapshot.docs.map((doc) => this.documentToObject<T>(doc));
      this.logger?.debug('Documents listed', { collection: collectionName, count: results.length });

      return results;
    } catch (error) {
      this.logger?.error('List failed', { collection: collectionName, options }, error as Error);
      throw new DataServiceError(
        `Failed to list documents from ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.QUERY_TIMEOUT,
        error,
      );
    }
  }

  async query<T>(collectionName: string, options: QueryOptions): Promise<T[]> {
    return this.list<T>(collectionName, options);
  }

  async findOne<T>(collectionName: string, options: QueryOptions): Promise<T | null> {
    try {
      const limitedOptions = { ...options, limit: 1 };
      const results = await this.list<T>(collectionName, limitedOptions);

      return results.length > 0 ? (results[0] ?? null) : null;
    } catch (error) {
      this.logger?.error('FindOne failed', { collection: collectionName, options }, error as Error);
      throw new DataServiceError(
        `Failed to find document in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.QUERY_TIMEOUT,
        error,
      );
    }
  }

  async count(collectionName: string, options?: QueryOptions): Promise<number> {
    try {
      // Firebase'de count query'si için getCountFromServer kullanılabilir
      // Şimdilik documents'ları çekip sayıyoruz
      const results = await this.list(collectionName, options);
      return results.length;
    } catch (error) {
      this.logger?.error('Count failed', { collection: collectionName, options }, error as Error);
      throw new DataServiceError(
        `Failed to count documents in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.QUERY_TIMEOUT,
        error,
      );
    }
  }

  // ===========================================
  // BATCH İŞLEMLER
  // ===========================================

  async batch(operations: BatchOperation[]): Promise<void> {
    try {
      const firestore = this.ensureInitialized();
      const batch = writeBatch(firestore);

      for (const operation of operations) {
        switch (operation.type) {
          case 'create':
            if (operation.data) {
              const docRef = doc(firestore, operation.collection);
              batch.set(docRef, operation.data as DocumentData);
            }
            break;

          case 'update':
            if (operation.id && operation.data) {
              const docRef = doc(firestore, operation.collection, operation.id);
              batch.update(docRef, operation.data as DocumentData);
            }
            break;

          case 'delete':
            if (operation.id) {
              const docRef = doc(firestore, operation.collection, operation.id);
              batch.delete(docRef);
            }
            break;
        }
      }

      await batch.commit();
      this.logger?.debug('Batch operations completed', { operationCount: operations.length });
    } catch (error) {
      this.logger?.error('Batch failed', { operations }, error as Error);
      throw new DataServiceError(
        'Failed to execute batch operations',
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  async createMany<T>(collectionName: string, data: T[]): Promise<string[]> {
    try {
      const firestore = this.ensureInitialized();
      const batch = writeBatch(firestore);
      const ids: string[] = [];

      for (const item of data) {
        const docRef = doc(firestore, collectionName);
        batch.set(docRef, item as DocumentData);
        ids.push(docRef.id);
      }

      await batch.commit();
      this.logger?.debug('Multiple documents created', {
        collection: collectionName,
        count: data.length,
      });

      return ids;
    } catch (error) {
      this.logger?.error(
        'CreateMany failed',
        { collection: collectionName, count: data.length },
        error as Error,
      );
      throw new DataServiceError(
        `Failed to create multiple documents in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  async updateMany<T>(
    collectionName: string,
    updates: Array<{ id: string; data: Partial<T> }>,
  ): Promise<void> {
    try {
      const firestore = this.ensureInitialized();
      const batch = writeBatch(firestore);

      for (const update of updates) {
        const docRef = doc(firestore, collectionName, update.id);
        batch.update(docRef, update.data as DocumentData);
      }

      await batch.commit();
      this.logger?.debug('Multiple documents updated', {
        collection: collectionName,
        count: updates.length,
      });
    } catch (error) {
      this.logger?.error(
        'UpdateMany failed',
        { collection: collectionName, count: updates.length },
        error as Error,
      );
      throw new DataServiceError(
        `Failed to update multiple documents in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  async deleteMany(collectionName: string, ids: string[]): Promise<void> {
    try {
      const firestore = this.ensureInitialized();
      const batch = writeBatch(firestore);

      for (const id of ids) {
        const docRef = doc(firestore, collectionName, id);
        batch.delete(docRef);
      }

      await batch.commit();
      this.logger?.debug('Multiple documents deleted', {
        collection: collectionName,
        count: ids.length,
      });
    } catch (error) {
      this.logger?.error(
        'DeleteMany failed',
        { collection: collectionName, count: ids.length },
        error as Error,
      );
      throw new DataServiceError(
        `Failed to delete multiple documents from ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  // ===========================================
  // REAL-TIME SUBSCRIPTIONS
  // ===========================================

  subscribe<T>(
    collectionName: string,
    callback: SubscriptionCallback<T>,
    options?: QueryOptions,
  ): UnsubscribeFunction {
    try {
      const queryRef = this.buildQuery(collectionName, options);

      const unsubscribe: Unsubscribe = onSnapshot(
        queryRef,
        (querySnapshot) => {
          const results = querySnapshot.docs.map((doc) => this.documentToObject<T>(doc));
          callback(results);
          this.logger?.debug('Subscription data received', {
            collection: collectionName,
            count: results.length,
          });
        },
        (error) => {
          this.logger?.error('Subscription error', { collection: collectionName }, error);
        },
      );

      this.logger?.debug('Subscription created', { collection: collectionName });
      return unsubscribe;
    } catch (error) {
      this.logger?.error(
        'Subscribe failed',
        { collection: collectionName, options },
        error as Error,
      );
      throw new DataServiceError(
        `Failed to subscribe to ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  subscribeToDocument<T>(
    collectionName: string,
    id: string,
    callback: (data: T | null) => void,
  ): UnsubscribeFunction {
    try {
      const firestore = this.ensureInitialized();
      const docRef = doc(firestore, collectionName, id);

      const unsubscribe: Unsubscribe = onSnapshot(
        docRef,
        (docSnap) => {
          if (docSnap.exists()) {
            const result = this.documentToObject<T>(docSnap);
            callback(result);
          } else {
            callback(null);
          }
          this.logger?.debug('Document subscription data received', {
            collection: collectionName,
            id,
          });
        },
        (error) => {
          this.logger?.error(
            'Document subscription error',
            { collection: collectionName, id },
            error,
          );
        },
      );

      this.logger?.debug('Document subscription created', { collection: collectionName, id });
      return unsubscribe;
    } catch (error) {
      this.logger?.error(
        'SubscribeToDocument failed',
        { collection: collectionName, id },
        error as Error,
      );
      throw new DataServiceError(
        `Failed to subscribe to document ${id} in ${collectionName}`,
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }

  // ===========================================
  // TRANSACTION DESTEĞI
  // ===========================================

  async runTransaction<T>(callback: TransactionCallback<T>): Promise<T> {
    try {
      const firestore = this.ensureInitialized();

      const result = await runTransaction(firestore, async (transaction) => {
        const wrapper = new FirebaseTransactionWrapper(transaction, firestore, this.logger);
        return await callback(wrapper);
      });

      this.logger?.debug('Transaction completed successfully');
      return result;
    } catch (error) {
      this.logger?.error('Transaction failed', {}, error as Error);
      throw new DataServiceError(
        'Transaction failed',
        DATA_SERVICE_ERROR_CODES.UNKNOWN_ERROR,
        error,
      );
    }
  }
}
