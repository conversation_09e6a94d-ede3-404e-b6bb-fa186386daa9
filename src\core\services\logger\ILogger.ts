/**
 * Logger Service Interface
 *
 * Bu interface, uygulamanın loglama ihtiyaçlarını karşılayan
 * metodları tanımlar. SOLID prensiplerine uygun olarak
 * implementasyon detaylarından bağımsız bir soyutlama sunar.
 *
 * Özellikler:
 * - Seviye bazlı loglama (error, warn, info, debug, trace)
 * - Structured logging desteği
 * - Context bilgisi ekleme
 * - Performance-friendly
 * - Tip güvenli
 */

import type { LogLevel } from 'src/core/config/IConfigService';

/**
 * Log entry için temel veri yapısı
 */
export interface LogEntry {
  /** Log seviyesi */
  level: LogLevel;
  /** Log mesajı */
  message: string;
  /** Timestamp (ISO string) */
  timestamp: string;
  /** Ek context bilgileri */
  context?: Record<string, unknown>;
  /** Hata objesi (varsa) */
  error?: Error;
  /** Log ka<PERSON> (component, service adı vb.) */
  source?: string;
  /** Kullanıcı ID'si (varsa) */
  userId?: string;
  /** Session ID'si (varsa) */
  sessionId?: string;
}

/**
 * Logger konfigürasyon seçenekleri
 */
export interface LoggerOptions {
  /** Minimum log seviyesi */
  minLevel?: LogLevel;
  /** Varsayılan context bilgileri */
  defaultContext?: Record<string, unknown>;
  /** Log formatı */
  format?: 'json' | 'text';
  /** Console'a yazdırma aktif mi */
  enableConsole?: boolean;
  /** Remote logging aktif mi */
  enableRemote?: boolean;
}

/**
 * Logger Service Interface
 *
 * Tüm loglama işlemlerini yöneten ana interface.
 * ConfigService'den log seviyesini alır ve buna göre
 * logları filtreler.
 */
export interface ILogger {
  // ===========================================
  // TEMEL LOG METODLARI
  // ===========================================

  /**
   * Error seviyesinde log yazdırır
   * @param message Log mesajı
   * @param context Ek context bilgileri
   * @param error Hata objesi (varsa)
   */
  error(message: string, context?: Record<string, unknown>, error?: Error): void;

  /**
   * Warning seviyesinde log yazdırır
   * @param message Log mesajı
   * @param context Ek context bilgileri
   */
  warn(message: string, context?: Record<string, unknown>): void;

  /**
   * Info seviyesinde log yazdırır
   * @param message Log mesajı
   * @param context Ek context bilgileri
   */
  info(message: string, context?: Record<string, unknown>): void;

  /**
   * Debug seviyesinde log yazdırır
   * @param message Log mesajı
   * @param context Ek context bilgileri
   */
  debug(message: string, context?: Record<string, unknown>): void;

  /**
   * Trace seviyesinde log yazdırır
   * @param message Log mesajı
   * @param context Ek context bilgileri
   */
  trace(message: string, context?: Record<string, unknown>): void;

  // ===========================================
  // STRUCTURED LOGGING METODLARI
  // ===========================================

  /**
   * Structured log entry yazdırır
   * @param entry Log entry objesi
   */
  log(entry: LogEntry): void;

  /**
   * Belirli seviyede log yazdırır
   * @param level Log seviyesi
   * @param message Log mesajı
   * @param context Ek context bilgileri
   * @param error Hata objesi (varsa)
   */
  logWithLevel(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error,
  ): void;

  // ===========================================
  // CONTEXT YÖNETİMİ
  // ===========================================

  /**
   * Varsayılan context bilgilerini ayarlar
   * @param context Context bilgileri
   */
  setDefaultContext(context: Record<string, unknown>): void;

  /**
   * Mevcut varsayılan context'i döndürür
   * @returns Varsayılan context
   */
  getDefaultContext(): Record<string, unknown>;

  /**
   * Context bilgilerini temizler
   */
  clearDefaultContext(): void;

  // ===========================================
  // KONFIGÜRASYON VE KONTROL
  // ===========================================

  /**
   * Belirli log seviyesinin aktif olup olmadığını kontrol eder
   * @param level Kontrol edilecek seviye
   * @returns Seviye aktif mi
   */
  isLevelEnabled(level: LogLevel): boolean;

  /**
   * Mevcut minimum log seviyesini döndürür
   * @returns Minimum log seviyesi
   */
  getMinLevel(): LogLevel;

  /**
   * Logger konfigürasyonunu günceller
   * @param options Yeni konfigürasyon seçenekleri
   */
  configure(options: Partial<LoggerOptions>): void;

  // ===========================================
  // PERFORMANCE VE UTILITY
  // ===========================================

  /**
   * Performance ölçümü başlatır
   * @param label Performance ölçüm etiketi
   * @returns Performance timer ID
   */
  startTimer(label: string): string;

  /**
   * Performance ölçümünü bitirir ve loglar
   * @param timerId Timer ID
   * @param context Ek context bilgileri
   */
  endTimer(timerId: string, context?: Record<string, unknown>): void;

  /**
   * Fonksiyon çalışma süresini ölçer ve loglar
   * @param label Ölçüm etiketi
   * @param fn Ölçülecek fonksiyon
   * @returns Fonksiyon sonucu
   */
  time<T>(label: string, fn: () => T): T;

  /**
   * Async fonksiyon çalışma süresini ölçer ve loglar
   * @param label Ölçüm etiketi
   * @param fn Ölçülecek async fonksiyon
   * @returns Promise ile fonksiyon sonucu
   */
  timeAsync<T>(label: string, fn: () => Promise<T>): Promise<T>;

  // ===========================================
  // GRUP VE BATCH LOGGING
  // ===========================================

  /**
   * Log grubunu başlatır
   * @param label Grup etiketi
   * @param collapsed Grup kapalı mı başlasın
   */
  group(label: string, collapsed?: boolean): void;

  /**
   * Log grubunu bitirir
   */
  groupEnd(): void;

  /**
   * Birden fazla log'u batch olarak yazdırır
   * @param entries Log entry'leri
   */
  batch(entries: LogEntry[]): void;
}

/**
 * Logger seviye öncelikleri
 * Düşük sayı = yüksek öncelik
 */
export const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  trace: 4,
} as const;

/**
 * Logger seviye renkleri (console için)
 */
export const LOG_LEVEL_COLORS: Record<LogLevel, string> = {
  error: '#ff4757', // Kırmızı
  warn: '#ffa502', // Turuncu
  info: '#3742fa', // Mavi
  debug: '#2ed573', // Yeşil
  trace: '#747d8c', // Gri
} as const;

/**
 * Logger seviye emojileri
 */
export const LOG_LEVEL_EMOJIS: Record<LogLevel, string> = {
  error: '❌',
  warn: '⚠️',
  info: 'ℹ️',
  debug: '🐛',
  trace: '🔍',
} as const;
