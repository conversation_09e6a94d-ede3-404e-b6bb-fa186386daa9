# Logger Servisi

Bu <PERSON>, Logger Servisi'nin nasıl kullanılacağını ve yapılandırılacağını açıklar.

## Genel Bakış

Logger Servisi, uygulamanın tüm loglama ihtiyaçlarını karşılayan merkezi bir servistir. SOLID prensiplerine uygun olarak tasarlanmış ve ConfigService ile entegre çalışır.

### Özellikler

- ✅ **Seviye Bazlı Loglama**: error, warn, info, debug, trace
- ✅ **Structured Logging**: JSON formatında detaylı log kayıtları
- ✅ **Context Yönetimi**: Her log'a ek bilgi ekleme
- ✅ **Performance Ölçümü**: Fonksiyon ve işlem sürelerini ölçme
- ✅ **Grup Loglama**: İlgili logları gruplama
- ✅ **Tip Gü<PERSON>ği**: TypeScript ile tam tip desteği
- ✅ **Konfigürasyonla Entegrasyon**: .env dosyasından log seviyesi kontrolü
- ✅ **Fallback Desteği**: Hata durumunda çalışmaya devam etme

## Kurulum ve Yapılandırma

### 1. Ortam Değişkenleri

`.env.local` dosyasında log seviyesini ayarlayın:

```env
VITE_LOG_LEVEL="debug"  # error, warn, info, debug, trace
```

### 2. Boot Dosyası

Logger servisi `src/boot/logger.ts` dosyasında otomatik olarak başlatılır ve `quasar.config.ts`'de boot listesine eklenmiştir.

## Kullanım

### 1. Composable ile Kullanım (Önerilen)

```typescript
import { useLogger } from 'src/composables/useLogger';

export default defineComponent({
  setup() {
    const logger = useLogger();

    onMounted(() => {
      logger.info('Component mounted', { componentName: 'MyComponent' });
    });

    return {};
  },
});
```

### 2. Dependency Injection ile Kullanım

```typescript
import { inject } from 'vue';
import { loggerKey } from 'src/constants/keys';

export default defineComponent({
  setup() {
    const logger = inject(loggerKey)!;

    logger.debug('Component setup completed');

    return {};
  },
});
```

### 3. Servis Sınıflarında Kullanım

```typescript
import type { ILogger } from 'src/core/services/logger/ILogger';

export class UserService {
  constructor(private logger: ILogger) {}

  async createUser(userData: UserData) {
    this.logger.info('Creating new user', { userData });

    try {
      const user = await this.apiCall(userData);
      this.logger.info('User created successfully', { userId: user.id });
      return user;
    } catch (error) {
      this.logger.error('Failed to create user', { userData }, error);
      throw error;
    }
  }
}
```

## Log Seviyeleri

### Seviye Hiyerarşisi

1. **error** (0) - Kritik hatalar
2. **warn** (1) - Uyarılar
3. **info** (2) - Genel bilgiler
4. **debug** (3) - Debug bilgileri
5. **trace** (4) - Detaylı izleme

### Seviye Filtreleme

Ortam değişkeninde ayarlanan seviye ve altındaki tüm seviyeler loglanır:

- `VITE_LOG_LEVEL="error"` → Sadece error logları
- `VITE_LOG_LEVEL="warn"` → error + warn logları
- `VITE_LOG_LEVEL="info"` → error + warn + info logları
- `VITE_LOG_LEVEL="debug"` → error + warn + info + debug logları
- `VITE_LOG_LEVEL="trace"` → Tüm loglar

## Kullanım Örnekleri

### Temel Loglama

```typescript
const logger = useLogger();

// Basit mesaj
logger.info('User logged in');

// Context ile
logger.info('User logged in', {
  userId: 123,
  timestamp: new Date().toISOString(),
});

// Hata loglama
try {
  await riskyOperation();
} catch (error) {
  logger.error('Operation failed', { operation: 'riskyOperation' }, error);
}
```

### Performance Ölçümü

```typescript
const logger = useLogger();

// Manuel timer
const timerId = logger.startTimer('API Call');
await apiCall();
logger.endTimer(timerId);

// Fonksiyon wrapper
const result = logger.time('Database Query', () => {
  return database.query('SELECT * FROM users');
});

// Async fonksiyon wrapper
const result = await logger.timeAsync('API Request', async () => {
  return await fetch('/api/data');
});
```

### Grup Loglama

```typescript
const logger = useLogger();

logger.group('User Registration Process');
logger.info('Validating user data');
logger.info('Checking email uniqueness');
logger.info('Hashing password');
logger.info('Saving to database');
logger.info('Sending welcome email');
logger.groupEnd();

// Kapalı grup
logger.group('Internal Process', true); // collapsed
logger.debug('Step 1');
logger.debug('Step 2');
logger.groupEnd();
```

### Context Yönetimi

```typescript
const logger = useLogger();

// Varsayılan context ayarlama
logger.setDefaultContext({
  userId: currentUser.id,
  sessionId: sessionId,
  version: appVersion,
});

// Artık tüm loglar bu context'i içerecek
logger.info('User action'); // userId, sessionId, version otomatik eklenir

// Context temizleme
logger.clearDefaultContext();
```

### Structured Logging

```typescript
const logger = useLogger();

// Detaylı log entry
logger.log({
  level: 'info',
  message: 'User performed action',
  timestamp: new Date().toISOString(),
  context: {
    userId: 123,
    action: 'purchase',
    productId: 456,
    amount: 99.99,
  },
  source: 'PaymentService',
});
```

## Güvenli Kullanım

### Fallback Logger

```typescript
import { useSafeLogger } from 'src/composables/useLogger';

// Hata durumunda bile çalışır
const logger = useSafeLogger();
logger.info('This will always work');
```

### Opsiyonel Logger

```typescript
import { useOptionalLogger } from 'src/composables/useLogger';

const logger = useOptionalLogger();
if (logger) {
  logger.debug('Logger is available');
}
```

## Best Practices

### 1. Anlamlı Mesajlar

```typescript
// ❌ Kötü
logger.info('User action');

// ✅ İyi
logger.info('User completed purchase', {
  userId: 123,
  orderId: 456,
  amount: 99.99,
});
```

### 2. Uygun Seviye Seçimi

```typescript
// ❌ Kötü - info seviyesinde hata
logger.info('Database connection failed');

// ✅ İyi - error seviyesinde hata
logger.error('Database connection failed', { connectionString: 'xxx' }, error);
```

### 3. Hassas Bilgileri Loglama

```typescript
// ❌ Kötü - şifre loglanıyor
logger.info('User login attempt', { email, password });

// ✅ İyi - hassas bilgi loglanmıyor
logger.info('User login attempt', { email, passwordLength: password.length });
```

### 4. Performance Kritik Yerlerde

```typescript
// ❌ Kötü - her döngüde log
for (const item of items) {
  logger.debug('Processing item', { item });
  processItem(item);
}

// ✅ İyi - toplu log
logger.debug('Processing items', { itemCount: items.length });
for (const item of items) {
  processItem(item);
}
logger.debug('Items processed successfully');
```

## Gelişmiş Özellikler

### Batch Logging

```typescript
const logger = useLogger();

const logEntries = [
  { level: 'info', message: 'Step 1 completed', timestamp: new Date().toISOString() },
  { level: 'info', message: 'Step 2 completed', timestamp: new Date().toISOString() },
  { level: 'info', message: 'Step 3 completed', timestamp: new Date().toISOString() },
];

logger.batch(logEntries);
```

### Konfigürasyon Güncelleme

```typescript
const logger = useLogger();

// Logger ayarlarını runtime'da değiştirme
logger.configure({
  format: 'json',
  enableConsole: true,
  enableRemote: false,
});
```

## Troubleshooting

### Logger Bulunamıyor Hatası

```
Error: Logger service not found! Make sure logger.ts boot file is properly configured.
```

**Çözüm:**

1. `quasar.config.ts`'de boot listesinde `logger` olduğundan emin olun
2. `src/boot/logger.ts` dosyasının mevcut olduğunu kontrol edin
3. ConfigService'in düzgün çalıştığını kontrol edin

### Loglar Görünmüyor

**Çözüm:**

1. `.env.local` dosyasında `VITE_LOG_LEVEL` ayarını kontrol edin
2. Browser console'da log seviyesi filtrelerini kontrol edin
3. `logger.isLevelEnabled('debug')` ile seviye kontrolü yapın

### Performance Sorunları

**Çözüm:**

1. Production'da log seviyesini `error` veya `warn` yapın
2. Çok sık loglama yapmaktan kaçının
3. Büyük objeleri context'e eklemekten kaçının
