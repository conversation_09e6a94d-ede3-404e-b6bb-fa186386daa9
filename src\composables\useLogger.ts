/**
 * Logger Composable
 *
 * Bu composable, Vue bileşenlerinde Logger servisini kullanmak için
 * kolay bir interface sağlar. Dependency injection ile LoggerService'i
 * alır ve tip güvenli bir şekilde sunar.
 *
 * Özellikler:
 * - Tip güvenli logger erişimi
 * - Hata yönetimi
 * - <PERSON><PERSON> kullanım
 * - Vue 3 Composition API uyumlu
 */

import { inject } from 'vue';
import type { ILogger } from 'src/core/services/logger/ILogger';
import { loggerKey } from 'src/constants/keys';

/**
 * Logger Composable
 *
 * Vue bileşenlerinde logger kullanımı için composable.
 *
 * @returns Logger instance
 * @throws Error Logger bulunamadığında
 *
 * @example
 * ```typescript
 * // Bileşende kullanım
 * import { useLogger } from 'src/composables/useLogger';
 *
 * export default defineComponent({
 *   setup() {
 *     const logger = useLogger();
 *
 *     onMounted(() => {
 *       logger.info('Component mounted', { componentName: 'MyComponent' });
 *     });
 *
 *     const handleError = (error: Error) => {
 *       logger.error('An error occurred', { action: 'handleError' }, error);
 *     };
 *
 *     return {
 *       handleError
 *     };
 *   }
 * });
 * ```
 */
export function useLogger(): ILogger {
  const logger = inject<ILogger>(loggerKey);

  if (!logger) {
    // Logger bulunamadı - bu kritik bir hata
    const errorMessage =
      'Logger service not found! Make sure logger.ts boot file is properly configured.';
    console.error('❌ [useLogger]', errorMessage);
    throw new Error(errorMessage);
  }

  return logger;
}

/**
 * Safe Logger Composable
 *
 * Logger bulunamadığında hata fırlatmak yerine fallback logger döndürür.
 * Bu, logger olmadan da çalışması gereken bileşenler için kullanılabilir.
 *
 * @returns Logger instance veya fallback logger
 *
 * @example
 * ```typescript
 * // Güvenli kullanım
 * import { useSafeLogger } from 'src/composables/useLogger';
 *
 * export default defineComponent({
 *   setup() {
 *     const logger = useSafeLogger();
 *
 *     // Logger yoksa bile çalışır
 *     logger.info('This will always work');
 *
 *     return {};
 *   }
 * });
 * ```
 */
export function useSafeLogger(): ILogger {
  const logger = inject<ILogger>(loggerKey);

  if (!logger) {
    // Fallback logger döndür
    console.warn('⚠️ [useSafeLogger] Logger service not found, using fallback logger');

    return createFallbackLogger();
  }

  return logger;
}

/**
 * Conditional Logger Composable
 *
 * Logger'ın mevcut olup olmadığını kontrol eder ve optional olarak döndürür.
 * Bu, logger'ın opsiyonel olduğu durumlar için kullanılır.
 *
 * @returns Logger instance veya undefined
 *
 * @example
 * ```typescript
 * // Opsiyonel kullanım
 * import { useOptionalLogger } from 'src/composables/useLogger';
 *
 * export default defineComponent({
 *   setup() {
 *     const logger = useOptionalLogger();
 *
 *     if (logger) {
 *       logger.info('Logger is available');
 *     }
 *
 *     return {};
 *   }
 * });
 * ```
 */
export function useOptionalLogger(): ILogger | undefined {
  return inject<ILogger>(loggerKey);
}

/**
 * Fallback logger oluşturur
 * Logger servisi bulunamadığında kullanılır
 */
function createFallbackLogger(): ILogger {
  return {
    error: (message: string, context?: Record<string, unknown>, error?: Error) => {
      console.error(`[FALLBACK ERROR] ${message}`, context, error);
    },
    warn: (message: string, context?: Record<string, unknown>) => {
      console.warn(`[FALLBACK WARN] ${message}`, context);
    },
    info: (message: string, context?: Record<string, unknown>) => {
      console.info(`[FALLBACK INFO] ${message}`, context);
    },
    debug: (message: string, context?: Record<string, unknown>) => {
      console.debug(`[FALLBACK DEBUG] ${message}`, context);
    },
    trace: (message: string, context?: Record<string, unknown>) => {
      console.trace(`[FALLBACK TRACE] ${message}`, context);
    },
    log: () => {
      console.warn('[FALLBACK] log() method not implemented in fallback logger');
    },
    logWithLevel: (level, message, context, error) => {
      console.log(`[FALLBACK ${level.toUpperCase()}] ${message}`, context, error);
    },
    setDefaultContext: () => {
      console.warn('[FALLBACK] setDefaultContext() method not implemented in fallback logger');
    },
    getDefaultContext: () => ({}),
    clearDefaultContext: () => {
      console.warn('[FALLBACK] clearDefaultContext() method not implemented in fallback logger');
    },
    isLevelEnabled: () => true,
    getMinLevel: () => 'error' as const,
    configure: () => {
      console.warn('[FALLBACK] configure() method not implemented in fallback logger');
    },
    startTimer: (label: string) => {
      console.time(`[FALLBACK TIMER] ${label}`);
      return `fallback_${label}_${Date.now()}`;
    },
    endTimer: (timerId: string) => {
      console.timeEnd(`[FALLBACK TIMER] ${timerId}`);
    },
    time: <T>(label: string, fn: () => T) => {
      console.time(`[FALLBACK TIME] ${label}`);
      try {
        const result = fn();
        console.timeEnd(`[FALLBACK TIME] ${label}`);
        return result;
      } catch (error) {
        console.timeEnd(`[FALLBACK TIME] ${label}`);
        throw error;
      }
    },
    timeAsync: async <T>(label: string, fn: () => Promise<T>) => {
      console.time(`[FALLBACK TIME ASYNC] ${label}`);
      try {
        const result = await fn();
        console.timeEnd(`[FALLBACK TIME ASYNC] ${label}`);
        return result;
      } catch (error) {
        console.timeEnd(`[FALLBACK TIME ASYNC] ${label}`);
        throw error;
      }
    },
    group: (label: string, collapsed = false) => {
      if (collapsed) {
        console.groupCollapsed(`[FALLBACK GROUP] ${label}`);
      } else {
        console.group(`[FALLBACK GROUP] ${label}`);
      }
    },
    groupEnd: () => {
      console.groupEnd();
    },
    batch: (entries) => {
      console.warn('[FALLBACK] batch() method not fully implemented in fallback logger', entries);
    },
  };
}

/**
 * Logger Composable Kullanım Örnekleri:
 *
 * 1. Temel Kullanım:
 *    const logger = useLogger();
 *    logger.info('Hello World');
 *
 * 2. Context ile Kullanım:
 *    const logger = useLogger();
 *    logger.info('User action', { userId: 123, action: 'login' });
 *
 * 3. Hata Loglama:
 *    const logger = useLogger();
 *    try {
 *      // some code
 *    } catch (error) {
 *      logger.error('Operation failed', { operation: 'saveData' }, error);
 *    }
 *
 * 4. Performance Ölçümü:
 *    const logger = useLogger();
 *    const timerId = logger.startTimer('API Call');
 *    await apiCall();
 *    logger.endTimer(timerId);
 *
 * 5. Grup Loglama:
 *    const logger = useLogger();
 *    logger.group('User Registration Process');
 *    logger.info('Validating user data');
 *    logger.info('Saving to database');
 *    logger.info('Sending welcome email');
 *    logger.groupEnd();
 *
 * 6. Güvenli Kullanım:
 *    const logger = useSafeLogger(); // Hata fırlatmaz
 *    logger.info('This always works');
 *
 * 7. Opsiyonel Kullanım:
 *    const logger = useOptionalLogger();
 *    if (logger) {
 *      logger.debug('Optional logging');
 *    }
 */
