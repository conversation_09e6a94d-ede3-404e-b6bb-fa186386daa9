# Modules Dizini

Bu dizin **özellik modüllerini** içerir.

## Amaç

- Uygulamanın ana özelliklerini modüler şekilde organize etme
- Her modülün kendi bağımsız yapısına sahip olması
- Modüller arası gevşek bağlılık (loose coupling)

## Modül Yapısı

Her modül aşağıdaki dizin yapısına sahip olmalıdır:

```
src/modules/<module-name>/
├── components/          # Modül-spesifik bileşenler
├── composables/         # Modül-spesifik composable'lar
├── constants/           # Modül-spesifik sabitler
├── pages/              # Modül sayfaları
├── router/             # Modül rotaları (routes.ts)
├── services/           # Modül servisleri
├── stores/             # Modül Pinia store'ları
├── types/              # Modül TypeScript tipleri
├── utils/              # Modül yardımcı fonksiyonları
├── i18n/               # Modül çevirileri
│   ├── en-US.ts
│   └── tr-TR.ts
└── index.ts            # Modül public API'si (opsiyonel)
```

## Örnek Modüller

- `auth/` - Kimlik doğrulama modülü
- `todo/` - Todo list modülü
- `profile/` - Kullanıcı profili modülü
- `dashboard/` - Dashboard modülü

## Kurallar

- Her modül kendi sorumluluğuna odaklanmalı
- Modüller arası doğrudan bağımlılık olmamalı
- Paylaşılan logic `src/core/` veya `src/composables/` dizininde olmalı
- Modül rotaları ana router'a entegre edilmeli
- Modül çevirileri ana i18n sistemine entegre edilmeli

## Modül Entegrasyonu

- Router entegrasyonu: `src/router/routes.ts`
- i18n entegrasyonu: `src/i18n/index.ts`
- Store entegrasyonu: Otomatik (Pinia)
