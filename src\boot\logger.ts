/**
 * Logger Boot File
 * 
 * Bu boot dosyası LoggerService'i başlatır ve uygulamaya provide eder.
 * ConfigService'i dependency injection ile alır ve LoggerService'e geçirir.
 * 
 * Boot sırası:
 * 1. config.ts (ConfigService)
 * 2. logger.ts (LoggerService) <- Bu dosya
 * 3. <PERSON>ğer servisler...
 * 
 * SOLID Prensipleri:
 * - Dependency Inversion: IConfigService abstraction'ına bağımlı
 * - Single Responsibility: Sadece Logger'ı başlatmaktan sorumlu
 */

import { boot } from 'quasar/wrappers';
import { inject } from 'vue';

// Servis ve interface import'ları
import { LoggerService } from 'src/core/services/logger/LoggerService';
import type { ILogger } from 'src/core/services/logger/ILogger';
import type { IConfigService } from 'src/core/config/IConfigService';

// Injection key'leri
import { configServiceKey, loggerKey } from 'src/constants/keys';

/**
 * Logger Boot Function
 * 
 * Bu fonksiyon:
 * 1. ConfigService'i inject eder
 * 2. LoggerService instance'ı oluşturur
 * 3. LoggerService'i global olarak provide eder
 * 4. Başlatma logunu yazdırır
 */
export default boot(({ app }) => {
  try {
    // ConfigService'i inject et
    // Bu noktada config.ts boot dosyası çalışmış olmalı
    const configService = inject<IConfigService>(configServiceKey);
    
    if (!configService) {
      // ConfigService bulunamadı - kritik hata
      console.error('❌ [LOGGER BOOT] ConfigService not found! Make sure config.ts boot file runs before logger.ts');
      throw new Error('ConfigService is required for LoggerService initialization');
    }

    // LoggerService instance'ı oluştur
    const loggerService: ILogger = new LoggerService(configService);
    
    // LoggerService'i global olarak provide et
    app.provide(loggerKey, loggerService);
    
    // Başarılı başlatma logunu yazdır
    loggerService.info('Logger service initialized successfully', {
      bootFile: 'logger.ts',
      minLogLevel: loggerService.getMinLevel(),
      environment: configService.isDevelopment() ? 'development' : 'production',
      appName: configService.getAppName(),
      appVersion: configService.getAppVersion(),
    });

    // Development modunda ek bilgiler
    if (configService.isDevelopment()) {
      loggerService.debug('Logger service configuration', {
        isLevelEnabled: {
          error: loggerService.isLevelEnabled('error'),
          warn: loggerService.isLevelEnabled('warn'),
          info: loggerService.isLevelEnabled('info'),
          debug: loggerService.isLevelEnabled('debug'),
          trace: loggerService.isLevelEnabled('trace'),
        },
        defaultContext: loggerService.getDefaultContext(),
      });
      
      // Logger test'i
      loggerService.trace('Logger test - trace level');
      loggerService.debug('Logger test - debug level');
      loggerService.info('Logger test - info level');
      loggerService.warn('Logger test - warn level');
      
      // Performance test
      const timerId = loggerService.startTimer('Logger Boot Performance Test');
      setTimeout(() => {
        loggerService.endTimer(timerId, { testType: 'boot-performance' });
      }, 1);
    }

  } catch (error) {
    // Boot hatası - console'a yazdır çünkü logger henüz hazır değil
    console.error('❌ [LOGGER BOOT] Failed to initialize logger service:', error);
    
    // Hata durumunda basit bir fallback logger oluştur
    const fallbackLogger: ILogger = {
      error: (message: string, context?: Record<string, unknown>, err?: Error) => {
        console.error(`[FALLBACK ERROR] ${message}`, context, err);
      },
      warn: (message: string, context?: Record<string, unknown>) => {
        console.warn(`[FALLBACK WARN] ${message}`, context);
      },
      info: (message: string, context?: Record<string, unknown>) => {
        console.info(`[FALLBACK INFO] ${message}`, context);
      },
      debug: (message: string, context?: Record<string, unknown>) => {
        console.debug(`[FALLBACK DEBUG] ${message}`, context);
      },
      trace: (message: string, context?: Record<string, unknown>) => {
        console.trace(`[FALLBACK TRACE] ${message}`, context);
      },
      log: () => {},
      logWithLevel: () => {},
      setDefaultContext: () => {},
      getDefaultContext: () => ({}),
      clearDefaultContext: () => {},
      isLevelEnabled: () => true,
      getMinLevel: () => 'error' as const,
      configure: () => {},
      startTimer: () => '',
      endTimer: () => {},
      time: <T>(label: string, fn: () => T) => fn(),
      timeAsync: async <T>(label: string, fn: () => Promise<T>) => fn(),
      group: () => {},
      groupEnd: () => {},
      batch: () => {},
    };
    
    // Fallback logger'ı provide et
    app.provide(loggerKey, fallbackLogger);
    
    // Fallback logger ile hata logla
    fallbackLogger.error('Logger service initialization failed, using fallback logger', {
      error: error instanceof Error ? error.message : String(error),
      bootFile: 'logger.ts',
    });
    
    // Hata durumunda da uygulama çalışmaya devam etsin
    // throw error; // Bu satırı comment'ledik çünkü uygulama çökmemeli
  }
});

/**
 * Logger Boot Dosyası Kullanım Notları:
 * 
 * 1. quasar.config.js'e ekleme:
 *    boot: ['config', 'logger', ...] // config'den sonra
 * 
 * 2. Bileşenlerde kullanım:
 *    import { inject } from 'vue';
 *    import { loggerKey } from 'src/constants/keys';
 *    
 *    const logger = inject(loggerKey)!;
 *    logger.info('Component mounted');
 * 
 * 3. Composable'larda kullanım:
 *    export function useLogger() {
 *      const logger = inject(loggerKey);
 *      if (!logger) {
 *        throw new Error('Logger not provided');
 *      }
 *      return logger;
 *    }
 * 
 * 4. Servis sınıflarında kullanım:
 *    constructor(private logger: ILogger) {}
 * 
 * 5. Hata durumları:
 *    - ConfigService bulunamadığında fallback logger kullanılır
 *    - Uygulama çökmez, sadece loglama kısıtlı olur
 *    - Development modunda detaylı hata bilgileri gösterilir
 */
