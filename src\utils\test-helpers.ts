/**
 * Test Yardımcı Fonksiyonları
 *
 * Bu dosya, unit testlerde dependency injection için
 * mock'lar ve test utility'le<PERSON>.
 */

import type { InjectionKey } from 'vue';

/**
 * Test için mock provider oluşturur
 *
 * @example
 * ```typescript
 * const mockProviders = createMockProviders({
 *   [configServiceKey]: mockConfigService,
 *   [loggerKey]: mockLogger
 * });
 *
 * // Test setup'ında
 * global: {
 *   provide: mockProviders
 * }
 * ```
 */
export function createMockProviders(mocks: Record<symbol, unknown>) {
  return Object.fromEntries(Object.entries(mocks).map(([key, value]) => [key, value]));
}

/**
 * Mock ConfigService oluşturur
 * Partial implementasyon sağlar, sadece test edilen metodları override etmek yeterli
 */
export function createMockConfigService(overrides: Partial<Record<string, unknown>> = {}) {
  return {
    getAppName: () => 'Test App',
    getAppVersion: () => '1.0.0-test',
    getDataSourceType: () => 'mock' as const,
    getApiBaseUrl: () => 'http://localhost:3000/api',
    getApiTimeout: () => 5000,
    getLogLevel: () => 'debug' as const,
    getDefaultLocale: () => 'en-US',
    getFallbackLocale: () => 'en-US',
    getDefaultTheme: () => 'light' as const,
    getNotificationDisplayLevel: () => 'info' as const,
    getApiErrorNotificationType: () => 'friendly' as const,
    getNotificationDefaultPosition: () => 'top-right' as const,
    getNotificationDefaultTimeout: () => 3000,
    getNotificationDefaultGroup: () => true,
    getNotificationDefaultCloseBtnLabel: () => '',
    isDevelopment: () => true,
    isProduction: () => false,
    ...overrides,
  };
}

/**
 * Mock Logger oluşturur
 */
export function createMockLogger(overrides: Partial<Record<string, unknown>> = {}) {
  const mockFn = () => undefined;
  return {
    error: mockFn,
    warn: mockFn,
    info: mockFn,
    debug: mockFn,
    trace: mockFn,
    ...overrides,
  };
}

/**
 * Mock DataService oluşturur
 */
export function createMockDataService(overrides: Partial<Record<string, unknown>> = {}) {
  const mockFn = () => Promise.resolve();
  return {
    get: mockFn,
    post: mockFn,
    put: mockFn,
    delete: mockFn,
    ...overrides,
  };
}

/**
 * Mock NotificationService oluşturur
 */
export function createMockNotificationService(overrides: Partial<Record<string, unknown>> = {}) {
  const mockFn = () => undefined;
  return {
    success: mockFn,
    error: mockFn,
    warning: mockFn,
    info: mockFn,
    ...overrides,
  };
}

/**
 * Vue Test Utils için global config oluşturur
 *
 * @example
 * ```typescript
 * const globalConfig = createTestGlobalConfig({
 *   configService: createMockConfigService(),
 *   logger: createMockLogger()
 * });
 *
 * mount(Component, { global: globalConfig });
 * ```
 */
export function createTestGlobalConfig(services: {
  configService?: unknown;
  logger?: unknown;
  dataService?: unknown;
  notificationService?: unknown;
  themeService?: unknown;
}) {
  // Import'lar test zamanında yapılacak
  // const { configServiceKey, loggerKey, ... } = await import('src/constants/keys');

  const provide: Record<symbol, unknown> = {};

  // Servisler varsa provide et
  if (services.configService) {
    // provide[configServiceKey as symbol] = services.configService;
  }

  if (services.logger) {
    // provide[loggerKey as symbol] = services.logger;
  }

  // Diğer servisler...

  return {
    provide,
    // Diğer global config'ler (plugins, directives, vb.)
  };
}

/**
 * Test için environment değişkenlerini mock'lar
 */
export function mockEnvironment(env: Partial<ImportMetaEnv>) {
  const originalEnv = import.meta.env;

  // Environment'ı mock'la
  Object.defineProperty(import.meta, 'env', {
    value: { ...originalEnv, ...env },
    writable: true,
  });

  // Cleanup fonksiyonu döndür
  return () => {
    Object.defineProperty(import.meta, 'env', {
      value: originalEnv,
      writable: true,
    });
  };
}

/**
 * Async injection için test helper'ı
 * Vue composition API'de inject'in async context'te kullanımı için
 */
export async function withInjection<T>(
  _key: InjectionKey<T>,
  _value: T,
  callback: () => Promise<void> | void,
) {
  // Bu fonksiyon test framework'üne göre implement edilecek
  // Jest, Vitest vb. için farklı implementasyonlar olabilir
  await callback();
}

/**
 * Test için genel setup fonksiyonu
 * Her test dosyasında kullanılabilir
 */
export function setupTestEnvironment() {
  // Global test setup'ları
  // Mock'lar, spy'lar vb.
  // Test framework'e göre implement edilecek
  // beforeEach(() => {
  //   // Her test öncesi cleanup
  // });
  // afterEach(() => {
  //   // Her test sonrası cleanup
  // });
}

/**
 * Component test için wrapper
 * Dependency injection ile component test etmek için
 */
export function createComponentTestWrapper(
  _component: unknown,
  options: {
    services?: Record<string, unknown>;
    props?: Record<string, unknown>;
    slots?: Record<string, unknown>;
  } = {},
) {
  const { services = {} } = options;

  // Bu fonksiyon Vue Test Utils'e göre implement edilecek
  createTestGlobalConfig(services);

  // return mount(component, {
  //   props,
  //   slots,
  //   global: globalConfig
  // });
}

// Type helpers for better TypeScript support in tests
export type MockFunction<T extends (...args: unknown[]) => unknown> = T;
export type MockObject<T> = {
  [K in keyof T]: T[K] extends (...args: unknown[]) => unknown ? MockFunction<T[K]> : T[K];
};

/**
 * Type-safe mock oluşturur
 */
export function createTypedMock<T>(): MockObject<T> {
  return {} as MockObject<T>;
}
