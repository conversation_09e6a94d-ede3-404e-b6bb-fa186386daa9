<template>
  <router-view />
</template>

<script setup lang="ts">
import { useLogger } from 'src/composables/useLogger';
import { useNotify } from 'src/composables/useNotify';
import { onMounted } from 'vue';

const logger = useLogger();
const notificationService = useNotify();

onMounted(() => {
  logger.info('App started successfully!');
  notificationService.success('App started successfully!');
});
</script>
