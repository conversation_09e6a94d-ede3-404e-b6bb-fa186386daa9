/**
 * Notification Service Interface
 * 
 * Bu interface, uygulamanın notification ihtiyaçlarını karşılayan
 * metodları tanımlar. SOLID prensiplerine uygun olarak
 * implementasyon detaylarından bağımsız bir soyutlama sunar.
 * 
 * Özellikler:
 * - Seviye bazlı notification (error, warn, info, success)
 * - API error handling
 * - Loading ve progress notifications
 * - Batch notifications
 * - Tip güvenli
 */

import type { NotificationLevel, ApiErrorNotificationType } from 'src/core/config/IConfigService';

/**
 * Notification entry için temel veri yapısı
 */
export interface NotificationEntry {
  /** Notification ID'si */
  id?: string;
  /** Notification seviyesi */
  level: NotificationLevel;
  /** Notification mesajı */
  message: string;
  /** Timestamp (ISO string) */
  timestamp: string;
  /** Ek context bilgileri */
  context?: Record<string, unknown>;
  /** Hata objesi (varsa) */
  error?: Error;
  /** Notification kaynağı */
  source?: string;
  /** Kullanıcı ID'si (varsa) */
  userId?: string;
  /** Session ID'si (varsa) */
  sessionId?: string;
  /** Timeout (milisaniye) */
  timeout?: number;
  /** Position */
  position?: string;
  /** Actions */
  actions?: Array<{
    label: string;
    handler: () => void;
    color?: string;
    icon?: string;
  }>;
}

/**
 * API Error notification için veri yapısı
 */
export interface ApiErrorNotification {
  /** Hata objesi */
  error: Error;
  /** Kullanıcı dostu mesaj */
  friendlyMessage?: string;
  /** Ek context bilgileri */
  context?: Record<string, unknown>;
  /** Notification type override */
  notificationType?: ApiErrorNotificationType;
  /** Retry action */
  retryAction?: () => void;
  /** Timeout override */
  timeout?: number;
}

/**
 * Loading notification için veri yapısı
 */
export interface LoadingNotification {
  /** Loading mesajı */
  message?: string;
  /** Spinner gösterilsin mi */
  spinner?: boolean;
  /** Progress değeri (0-100) */
  progress?: number;
  /** İptal edilebilir mi */
  cancellable?: boolean;
  /** İptal callback'i */
  onCancel?: () => void;
}

/**
 * Notification Service Interface
 * 
 * Tüm notification işlemlerini yöneten ana interface.
 * ConfigService'den notification ayarlarını alır ve buna göre
 * notification'ları filtreler ve gösterir.
 */
export interface INotificationService {
  // ===========================================
  // TEMEL NOTIFICATION METODLARI
  // ===========================================
  
  /**
   * Success notification gösterir
   * @param message Notification mesajı
   * @param context Ek context bilgileri
   * @returns Notification ID
   */
  success(message: string, context?: Record<string, unknown>): string | undefined;
  
  /**
   * Info notification gösterir
   * @param message Notification mesajı
   * @param context Ek context bilgileri
   * @returns Notification ID
   */
  info(message: string, context?: Record<string, unknown>): string | undefined;
  
  /**
   * Warning notification gösterir
   * @param message Notification mesajı
   * @param context Ek context bilgileri
   * @returns Notification ID
   */
  warning(message: string, context?: Record<string, unknown>): string | undefined;
  
  /**
   * Error notification gösterir
   * @param message Notification mesajı
   * @param context Ek context bilgileri
   * @param error Hata objesi (varsa)
   * @returns Notification ID
   */
  error(message: string, context?: Record<string, unknown>, error?: Error): string | undefined;

  // ===========================================
  // STRUCTURED NOTIFICATION METODLARI
  // ===========================================
  
  /**
   * Structured notification gösterir
   * @param entry Notification entry objesi
   * @returns Notification ID
   */
  notify(entry: NotificationEntry): string | undefined;
  
  /**
   * Belirli seviyede notification gösterir
   * @param level Notification seviyesi
   * @param message Notification mesajı
   * @param context Ek context bilgileri
   * @param error Hata objesi (varsa)
   * @returns Notification ID
   */
  notifyWithLevel(
    level: NotificationLevel, 
    message: string, 
    context?: Record<string, unknown>, 
    error?: Error
  ): string | undefined;

  // ===========================================
  // API ERROR HANDLING
  // ===========================================
  
  /**
   * API error notification gösterir
   * ConfigService'den alınan ayarlara göre davranır
   * @param options API error notification options
   * @returns Notification ID
   */
  apiError(options: ApiErrorNotification): string | undefined;

  // ===========================================
  // LOADING VE PROGRESS
  // ===========================================
  
  /**
   * Loading notification gösterir
   * @param options Loading notification options
   * @returns Notification ID
   */
  loading(options?: LoadingNotification): string | undefined;
  
  /**
   * Progress notification gösterir
   * @param message Progress mesajı
   * @param progress Progress değeri (0-100)
   * @param context Ek context bilgileri
   * @returns Notification ID
   */
  progress(message: string, progress: number, context?: Record<string, unknown>): string | undefined;
  
  /**
   * Loading notification'ı günceller
   * @param notificationId Notification ID
   * @param progress Yeni progress değeri
   * @param message Yeni mesaj (opsiyonel)
   */
  updateProgress(notificationId: string, progress: number, message?: string): void;

  // ===========================================
  // NOTIFICATION YÖNETİMİ
  // ===========================================
  
  /**
   * Belirli notification'ı kapatır
   * @param notificationId Notification ID
   */
  dismiss(notificationId: string): void;
  
  /**
   * Tüm notification'ları kapatır
   */
  dismissAll(): void;
  
  /**
   * Belirli seviyedeki notification'ları kapatır
   * @param level Notification seviyesi
   */
  dismissByLevel(level: NotificationLevel): void;

  // ===========================================
  // KONFIGÜRASYON VE KONTROL
  // ===========================================
  
  /**
   * Belirli notification seviyesinin aktif olup olmadığını kontrol eder
   * @param level Kontrol edilecek seviye
   * @returns Seviye aktif mi
   */
  isLevelEnabled(level: NotificationLevel): boolean;
  
  /**
   * Mevcut notification display seviyesini döndürür
   * @returns Notification display seviyesi
   */
  getDisplayLevel(): NotificationLevel;
  
  /**
   * API error notification tipini döndürür
   * @returns API error notification tipi
   */
  getApiErrorNotificationType(): ApiErrorNotificationType;

  // ===========================================
  // BATCH VE UTILITY
  // ===========================================
  
  /**
   * Birden fazla notification'ı batch olarak gösterir
   * @param entries Notification entry'leri
   * @returns Notification ID'leri
   */
  batch(entries: NotificationEntry[]): string[];
  
  /**
   * Notification geçmişini döndürür
   * @param limit Maksimum kayıt sayısı
   * @returns Notification geçmişi
   */
  getHistory(limit?: number): NotificationEntry[];
  
  /**
   * Notification istatistiklerini döndürür
   * @returns Notification istatistikleri
   */
  getStats(): {
    total: number;
    byLevel: Record<NotificationLevel, number>;
    active: number;
  };
}

/**
 * Notification seviye renkleri (UI için)
 */
export const NOTIFICATION_LEVEL_COLORS: Record<NotificationLevel, string> = {
  error: '#f44336',    // Kırmızı
  warn: '#ff9800',     // Turuncu
  info: '#2196f3',     // Mavi
  success: '#4caf50',  // Yeşil
  none: '#9e9e9e',     // Gri
} as const;

/**
 * Notification seviye ikonları
 */
export const NOTIFICATION_LEVEL_ICONS: Record<NotificationLevel, string> = {
  error: 'error',
  warn: 'warning',
  info: 'info',
  success: 'check_circle',
  none: 'notifications_none',
} as const;

/**
 * Notification varsayılan timeout değerleri (milisaniye)
 */
export const NOTIFICATION_DEFAULT_TIMEOUTS: Record<NotificationLevel, number> = {
  error: 8000,    // 8 saniye
  warn: 6000,     // 6 saniye
  info: 4000,     // 4 saniye
  success: 3000,  // 3 saniye
  none: 0,        // Timeout yok
} as const;
