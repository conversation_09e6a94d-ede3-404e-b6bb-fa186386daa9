/**
 * Notify Defaults Boot File
 * 
 * Bu boot dosyası Quasar Notify plugin'inin varsayılan ayarlarını
 * ConfigService'den alınan değerlere göre yapılandırır.
 * 
 * Boot sırası:
 * 1. config.ts (ConfigService)
 * 2. logger.ts (LoggerService)
 * 3. notify-defaults.ts (Notify Defaults) <- Bu dosya
 * 4. Diğer servisler...
 * 
 * SOLID Prensipleri:
 * - Dependency Inversion: IConfigService abstraction'ına bağımlı
 * - Single Responsibility: Sadece Notify varsayılan ayarlarını yapmaktan sorumlu
 */

import { boot } from 'quasar/wrappers';
import { Notify } from 'quasar';

// Servis ve interface import'ları
import type { IConfigService } from 'src/core/config/IConfigService';
import type { ILogger } from 'src/core/services/logger/ILogger';

// Injection key'leri
import { configServiceKey, loggerKey } from 'src/constants/keys';

/**
 * Notify Defaults Boot Function
 * 
 * Bu fonksiyon:
 * 1. ConfigService'i app context'inden alır
 * 2. Logger'ı app context'inden alır
 * 3. ConfigService'den notify ayarlarını okur
 * 4. Notify.setDefaults() ile varsayılan ayarları uygular
 * 5. Başlatma logunu yazdırır
 */
export default boot(({ app }) => {
  try {
    // ConfigService'i app context'inden al
    const configService = app._context.provides[configServiceKey as symbol] as IConfigService;
    
    if (!configService) {
      console.error('❌ [NOTIFY DEFAULTS BOOT] ConfigService not found! Make sure config.ts boot file runs before notify-defaults.ts');
      throw new Error('ConfigService is required for Notify defaults initialization');
    }

    // Logger'ı app context'inden al (opsiyonel)
    const logger = app._context.provides[loggerKey as symbol] as ILogger;

    // ConfigService'den notify ayarlarını al
    const notifyDefaults = {
      position: configService.getNotificationDefaultPosition(),
      timeout: configService.getNotificationDefaultTimeout(),
      group: configService.getNotificationDefaultGroup(),
      closeBtn: configService.getNotificationDefaultCloseBtnLabel(),
      // Ek varsayılan ayarlar
      multiLine: false,
      html: false,
      spinner: false,
      textColor: 'white',
      classes: 'glossy',
    };

    // Notify varsayılan ayarlarını uygula
    Notify.setDefaults(notifyDefaults);

    // Başarılı başlatma logunu yazdır
    if (logger) {
      logger.info('Notify defaults configured successfully', {
        bootFile: 'notify-defaults.ts',
        defaults: notifyDefaults,
        environment: configService.isDevelopment() ? 'development' : 'production',
      });

      // Development modunda test notifikasyonu
      if (configService.isDevelopment()) {
        logger.debug('Notify defaults configuration', {
          position: notifyDefaults.position,
          timeout: notifyDefaults.timeout,
          group: notifyDefaults.group,
          closeBtn: notifyDefaults.closeBtn,
        });
      }
    } else {
      // Logger yoksa console'a yazdır
      console.info('🔔 [NOTIFY DEFAULTS] Notify defaults configured successfully', notifyDefaults);
    }

  } catch (error) {
    // Boot hatası - console'a yazdır
    console.error('❌ [NOTIFY DEFAULTS BOOT] Failed to configure notify defaults:', error);
    
    // Hata durumunda minimal varsayılan ayarları uygula
    const fallbackDefaults = {
      position: 'top-right' as const,
      timeout: 5000,
      group: true,
      closeBtn: false,
      multiLine: false,
      html: false,
      spinner: false,
      textColor: 'white',
      classes: 'glossy',
    };

    try {
      Notify.setDefaults(fallbackDefaults);
      console.warn('⚠️ [NOTIFY DEFAULTS] Using fallback defaults due to configuration error', fallbackDefaults);
    } catch (fallbackError) {
      console.error('❌ [NOTIFY DEFAULTS] Failed to set even fallback defaults:', fallbackError);
    }
  }
});

/**
 * Notify Defaults Boot Dosyası Kullanım Notları:
 * 
 * 1. quasar.config.ts'e ekleme:
 *    boot: ['config', 'logger', 'notify-defaults', ...] // config ve logger'dan sonra
 * 
 * 2. Ortam değişkenleri (.env.local):
 *    VITE_NOTIFICATION_DEFAULT_POSITION="top-right"
 *    VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"
 *    VITE_NOTIFICATION_DEFAULT_GROUP="true"
 *    VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""
 * 
 * 3. Notify kullanımı (artık varsayılan ayarlarla):
 *    import { Notify } from 'quasar';
 *    
 *    Notify.create({
 *      message: 'Hello World',
 *      type: 'positive'
 *    }); // Varsayılan position, timeout vb. otomatik uygulanır
 * 
 * 4. Özel ayarlarla kullanım:
 *    Notify.create({
 *      message: 'Custom notification',
 *      type: 'warning',
 *      position: 'bottom', // Varsayılanı override eder
 *      timeout: 10000      // Varsayılanı override eder
 *    });
 * 
 * 5. Hata durumları:
 *    - ConfigService bulunamadığında fallback defaults kullanılır
 *    - Logger bulunamadığında console'a yazdırılır
 *    - Notify.setDefaults() başarısız olursa hata loglanır
 */
