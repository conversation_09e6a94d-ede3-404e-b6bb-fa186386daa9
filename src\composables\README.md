# Composables Dizini

Bu dizin **genel/paylaşılan Vue Composition API fonksiyonlarını** içerir.

## Amaç
- Yeniden kullanılabilir reactive logic
- Bileşenler arası paylaşılan state ve davranışlar
- Vue 3 Composition API'nin gücünden yararlanma

## Örnekler
- `useApi.ts` - API çağrıları için composable
- `useAuth.ts` - Kimlik doğrulama logic'i
- `useLocalStorage.ts` - Local storage yönetimi
- `useTheme.ts` - Tema yönetimi
- `useNotification.ts` - Notifikasyon yönetimi
- `useValidation.ts` - Form validasyon logic'i

## Kurallar
- `use` prefix'i ile başlamalı
- Reactive değerler döndürmeli (`ref`, `reactive`, `computed`)
- TypeScript ile tip güvenliği sağlanmalı
- Side effect'leri `onMounted`, `onUnmounted` ile yönetmeli
- Dependency injection için `inject()` kullanabilir

## Modül-Spesifik Composables
Sadece belirli bir modülde kullanılan composable'lar `src/modules/<module-name>/composables/` dizininde olmalıdır.
