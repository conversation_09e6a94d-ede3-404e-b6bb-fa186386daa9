# ===========================================
# ÖRNEK ORTAM DEĞİŞKENLERİ DOSYASI
# ===========================================
# Bu dosya, uygulamanın ihtiyaç duyduğu tüm ortam değişkenlerinin
# örnek değerlerini içerir. Gerçek değerler için bu dosyayı
# .env.local olarak kopyalayın ve değerleri güncelleyin.

# ===========================================
# UYGULAMA GENEL AYARLARI
# ===========================================
VITE_APP_NAME="Benim Modüler Quasar Uygulamam"
VITE_APP_VERSION="1.0.0"

# ===========================================
# VERİ KAYNAĞI AYARLARI
# ===========================================
# Olası değerler: "firebase", "restapi", "mock"
VITE_DATA_SOURCE_TYPE="firebase"

# ===========================================
# REST API AYARLARI
# ===========================================
# Bu ayarlar sadece VITE_DATA_SOURCE_TYPE="restapi" olduğunda kullanılır
VITE_API_BASE_URL="http://localhost:8080/api"
VITE_API_TIMEOUT="30000"

# ===========================================
# FIREBASE AYARLARI
# ===========================================
# Bu ayarlar sadece VITE_DATA_SOURCE_TYPE="firebase" olduğunda kullanılır
# Firebase Console'dan alınan gerçek değerlerle değiştirin
VITE_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY_PLACEHOLDER"
VITE_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN_PLACEHOLDER"
VITE_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID_PLACEHOLDER"
VITE_FIREBASE_STORAGE_BUCKET="YOUR_FIREBASE_STORAGE_BUCKET_PLACEHOLDER"
VITE_FIREBASE_MESSAGING_SENDER_ID="YOUR_FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER"
VITE_FIREBASE_APP_ID="YOUR_FIREBASE_APP_ID_PLACEHOLDER"
# Opsiyonel - Google Analytics için
VITE_FIREBASE_MEASUREMENT_ID="YOUR_FIREBASE_MEASUREMENT_ID_PLACEHOLDER"

# ===========================================
# ÇOKLU DİL (i18n) AYARLARI
# ===========================================
VITE_DEFAULT_LOCALE="tr-TR"
VITE_FALLBACK_LOCALE="en-US"

# ===========================================
# TEMA AYARLARI
# ===========================================
# Olası değerler: "light", "dark", "auto"
VITE_DEFAULT_THEME="light"

# ===========================================
# LOGLAMA AYARLARI
# ===========================================
# Olası değerler: "error", "warn", "info", "debug", "trace"
VITE_LOG_LEVEL="debug"

# ===========================================
# NOTİFİKASYON AYARLARI (Quasar Notify)
# ===========================================
# Hangi seviyedeki notifikasyonların gösterileceği
# Olası değerler: "error", "warn", "info", "success", "none"
VITE_NOTIFICATION_DISPLAY_LEVEL="info"

# API hatalarının nasıl gösterileceği
# Olası değerler: "toast", "friendly", "silent"
VITE_API_ERROR_NOTIFICATION_TYPE="friendly"

# Notifikasyon varsayılan pozisyonu
# Quasar Notify pozisyonları: "top-left", "top", "top-right", "left", "center", "right", "bottom-left", "bottom", "bottom-right"
VITE_NOTIFICATION_DEFAULT_POSITION="top-right"

# Notifikasyon varsayılan timeout süresi (milisaniye)
VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"

# Notifikasyonların gruplandırılması
# "true" veya "false"
VITE_NOTIFICATION_DEFAULT_GROUP="true"

# Notifikasyon kapatma butonu etiketi
# Boş = Quasar varsayılanı, veya ikon/metin
VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""
