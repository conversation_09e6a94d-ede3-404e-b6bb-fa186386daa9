/// <reference types="vite/client" />

/**
 * Vite ortam değişkenleri için TypeScript tip tanımları
 *
 * Bu dosya, import.meta.env için tip güvenliği sağlar.
 * Sadece ConfigService tarafından doğrudan okunacak ham değişkenlerin
 * tiplerini tanımlar. ConfigService bu ham değerleri işleyip
 * daha kullanışlı tiplerle sunar.
 */

interface ImportMetaEnv {
  // ===========================================
  // UYGULAMA GENEL AYARLARI
  // ===========================================
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;

  // ===========================================
  // VERİ KAYNAĞI AYARLARI
  // ===========================================
  readonly VITE_DATA_SOURCE_TYPE: string;

  // ===========================================
  // REST API AYARLARI
  // ===========================================
  readonly VITE_API_BASE_URL?: string;
  readonly VITE_API_TIMEOUT?: string;

  // ===========================================
  // FIREBASE AYARLARI
  // ===========================================
  readonly VITE_FIREBASE_API_KEY?: string;
  readonly VITE_FIREBASE_AUTH_DOMAIN?: string;
  readonly VITE_FIREBASE_PROJECT_ID?: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET?: string;
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID?: string;
  readonly VITE_FIREBASE_APP_ID?: string;
  readonly VITE_FIREBASE_MEASUREMENT_ID?: string;

  // ===========================================
  // ÇOKLU DİL (i18n) AYARLARI
  // ===========================================
  readonly VITE_DEFAULT_LOCALE: string;
  readonly VITE_FALLBACK_LOCALE: string;

  // ===========================================
  // TEMA AYARLARI
  // ===========================================
  readonly VITE_DEFAULT_THEME: string;

  // ===========================================
  // LOGLAMA AYARLARI
  // ===========================================
  readonly VITE_LOG_LEVEL: string;

  // ===========================================
  // NOTİFİKASYON AYARLARI
  // ===========================================
  readonly VITE_NOTIFICATION_DISPLAY_LEVEL: string;
  readonly VITE_API_ERROR_NOTIFICATION_TYPE: string;
  readonly VITE_NOTIFICATION_DEFAULT_POSITION: string;
  readonly VITE_NOTIFICATION_DEFAULT_TIMEOUT: string;
  readonly VITE_NOTIFICATION_DEFAULT_GROUP: string;
  readonly VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL: string;

  // ===========================================
  // VITE VARSAYILAN DEĞİŞKENLERİ
  // ===========================================
  readonly MODE: string;
  readonly BASE_URL: string;
  readonly PROD: boolean;
  readonly DEV: boolean;
  readonly SSR: boolean;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
