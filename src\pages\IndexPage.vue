<template>
  <q-page class="row items-center justify-evenly">
    <div class="column q-gutter-md">
      <h4>Notification Test</h4>

      <div class="row q-gutter-sm">
        <q-btn color="positive" @click="testSuccess" label="Test Success" />
        <q-btn color="info" @click="testInfo" label="Test Info" />
        <q-btn color="warning" @click="testWarning" label="Test Warning" />
        <q-btn color="negative" @click="testError" label="Test Error" />
      </div>

      <div class="row q-gutter-sm">
        <q-btn color="primary" @click="testLoading" label="Test Loading" />
        <q-btn color="secondary" @click="testApiError" label="Test API Error" />
        <q-btn color="accent" @click="testProgress" label="Test Progress" />
      </div>

      <div class="row q-gutter-sm">
        <q-btn color="dark" @click="testQuasarDirect" label="Test Quasar Direct" />
        <q-btn color="purple" @click="testDebug" label="Debug Info" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useNotify } from 'src/composables/useNotify';
import { useLogger } from 'src/composables/useLogger';
import { Notify } from 'quasar';

const notify = useNotify();
const logger = useLogger();

const testSuccess = () => {
  logger.info('Testing success notification');
  notify.success('This is a success notification!');
};

const testInfo = () => {
  logger.info('Testing info notification');
  notify.info('This is an info notification!');
};

const testWarning = () => {
  logger.info('Testing warning notification');
  notify.warning('This is a warning notification!');
};

const testError = () => {
  logger.info('Testing error notification');
  notify.error('This is an error notification!');
};

const testLoading = () => {
  logger.info('Testing loading notification');
  const loading = notify.loading('Loading data...');

  setTimeout(() => {
    if (loading) loading();
    notify.success('Loading completed!');
  }, 3000);
};

const testApiError = () => {
  logger.info('Testing API error notification');
  const error = new Error('Network connection failed');
  notify.apiError({
    error,
    friendlyMessage: 'Unable to connect to server. Please check your internet connection.',
    context: { endpoint: '/api/test', method: 'GET' },
  });
};

const testProgress = () => {
  logger.info('Testing progress notification');
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    notify.progress('Uploading file...', progress);

    if (progress >= 100) {
      clearInterval(interval);
      setTimeout(() => {
        notify.success('Upload completed!');
      }, 500);
    }
  }, 200);
};

const testQuasarDirect = () => {
  logger.info('Testing Quasar Notify directly');
  Notify.create({
    message: 'Direct Quasar Notify test',
    type: 'positive',
    position: 'top-right',
    timeout: 3000,
  });
};

const testDebug = () => {
  logger.info('Debug info for notifications');
  console.log('Notify service:', notify);
  console.log('Is success level enabled:', notify.isLevelEnabled('success'));
  console.log('Is info level enabled:', notify.isLevelEnabled('info'));
  console.log('Is error level enabled:', notify.isLevelEnabled('error'));

  // ConfigService debug
  interface WindowWithConfigService extends Window {
    __CONFIG_SERVICE__?: {
      getNotificationDisplayLevel(): string;
      getApiErrorNotificationType(): string;
      getNotificationDefaultPosition(): string;
    };
  }

  const configService = (window as WindowWithConfigService).__CONFIG_SERVICE__;
  if (configService) {
    console.log('Notification display level:', configService.getNotificationDisplayLevel());
    console.log('API error notification type:', configService.getApiErrorNotificationType());
    console.log('Notification default position:', configService.getNotificationDefaultPosition());
  }
};
</script>
