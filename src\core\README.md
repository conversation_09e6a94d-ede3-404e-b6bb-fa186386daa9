# Core Dizini

Bu dizin **uygulamanın çekirdek altyapısını** içerir.

## Amaç

- Uygulama geneli temel servisler
- Veri katmanı soyutlamaları
- Konfigürasyon yönetimi
- Tema yönetimi
- Temel TypeScript tipleri

## Alt Dizinler

### `config/`

- `IConfigService.ts` - Konfigürasyon servisi arayüzü
- `ConfigService.ts` - Konfigürasyon servisi implementasyonu
- Ortam değişkenlerini okur ve işler

### `data-layer/`

- `IDataService.ts` - Veri katmanı arayüzü
- `implementations/` - Farklı veri kaynağı implementasyonları
  - `firebase/` - Firebase implementasyonu
  - `rest/` - REST API implementasyonu
  - `mock/` - Mock veri implementasyonu

### `services/`

- `logger/` - Loglama servisi
- `notification/` - Notifikasyon servisi
- Uygulama geneli servisler

### `theme/`

- Tema yönetimi servisleri
- Quasar tema entegrasyonu

### `types/`

- Uygulama geneli TypeScript tip tanımları
- Interface'ler ve type'lar

## Prensipler

- **Dependency Inversion Principle (DIP)**: Interface'lere bağımlılık
- **Single Responsibility Principle (SRP)**: Her servis tek sorumluluğa sahip
- **Open/Closed Principle (OCP)**: Genişlemeye açık, değişime kapalı
